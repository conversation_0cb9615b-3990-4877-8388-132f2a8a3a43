console.log('🚀 Test simple iniciado');

try {
    console.log('📦 Cargando llrpjs...');
    const llrpjs = require('llrpjs');
    console.log('✅ llrpjs cargado');
    
    console.log('🔗 Creando cliente...');
    const client = new llrpjs.LLRPClient({
        host: '*************',
        port: 5084
    });
    
    console.log('📡 Intentando conectar...');
    
    client.on('connect', () => {
        console.log('✅ CONECTADO');
        client.disconnect();
    });
    
    client.on('error', (error) => {
        console.log('❌ ERROR:', error.message);
        process.exit(1);
    });
    
    client.on('disconnect', () => {
        console.log('✅ DESCONECTADO');
        process.exit(0);
    });
    
    client.connect();
    
    setTimeout(() => {
        console.log('⏰ TIMEOUT');
        process.exit(1);
    }, 5000);
    
} catch (error) {
    console.log('❌ EXCEPCIÓN:', error.message);
    process.exit(1);
}
