{"sourceFile": "app/zebra-rfid-tester.js", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1758058241457, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1758058268143, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -179,8 +179,9 @@\n         console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);\n     }\n \n     createDefaultROSpec() {\n+        // ROSpec basado en la configuración real del Zebra que funciona\n         return {\n             \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n             \"id\": 103,\n             \"type\": \"ADD_ROSPEC\",\n@@ -193,24 +194,25 @@\n                         \"ROSpecStartTrigger\": {\n                             \"ROSpecStartTriggerType\": \"Immediate\"\n                         },\n                         \"ROSpecStopTrigger\": {\n-                            \"ROSpecStopTriggerType\": \"Null\"\n+                            \"ROSpecStopTriggerType\": \"Immediate\"\n                         }\n                     },\n                     \"AISpec\": {\n-                        \"AntennaIDs\": [1, 2, 3, 4],\n+                        \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8], // Todas las 8 antenas como en la config real\n                         \"AISpecStopTrigger\": {\n-                            \"AISpecStopTriggerType\": \"Null\"\n+                            \"AISpecStopTriggerType\": \"Duration\",\n+                            \"DurationTriggerValue\": 400 // 400ms como en la config real\n                         },\n                         \"InventoryParameterSpec\": {\n                             \"InventoryParameterSpecID\": 1,\n                             \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n                         }\n                     },\n                     \"ROReportSpec\": {\n                         \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n-                        \"N\": 1,\n+                        \"N\": 1, // Tag Report Trigger: 1 como en la config real\n                         \"TagReportContentSelector\": {\n                             \"EnableROSpecID\": false,\n                             \"EnableSpecIndex\": false,\n                             \"EnableInventoryParameterSpecID\": false,\n"}, {"date": 1758058282605, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,755 @@\n+#!/usr/bin/env node\n+\n+/**\n+ * Zebra FX9600 RFID Reader Tester\n+ * Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+ *\n+ * Uso:\n+ *   node zebra-rfid-tester.js [opciones]\n+ *\n+ * Opciones:\n+ *   --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+ *   --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+ *   --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+ *   --host <ip>          IP del reader (override config)\n+ *   --port <puerto>      Puerto del reader (override config)\n+ *   --help               <PERSON>rar ayuda\n+ */\n+\n+const fs = require('fs');\n+const path = require('path');\n+\n+// Configuración por defecto\n+const DEFAULT_CONFIG = {\n+    host: '*************',\n+    port: 5084,\n+    timeout: 10000,\n+    reconnectInterval: 5000,\n+    maxReconnectAttempts: 5\n+};\n+\n+const DEFAULT_FILES = {\n+    config: 'zebra-config.json',\n+    rospec: 'zebra-rospec.json',\n+    output: 'zebra-tags.log'\n+};\n+\n+class ZebraRFIDTester {\n+    constructor(options = {}) {\n+        this.options = { ...DEFAULT_FILES, ...options };\n+        this.config = null;\n+        this.rospecConfig = null;\n+        this.connection = null;\n+        this.isConnected = false;\n+        this.isRunning = false;\n+        this.reconnectAttempts = 0;\n+        this.tagCount = 0;\n+        this.startTime = null;\n+        this.outputStream = null;\n+        this.gracefulShutdown = false;\n+\n+        // Estadísticas\n+        this.stats = {\n+            totalTags: 0,\n+            uniqueTags: new Set(),\n+            antennaStats: {},\n+            lastTagTime: null,\n+            startTime: null\n+        };\n+\n+        // Configurar manejo de señales mejorado\n+        this.setupSignalHandlers();\n+    }\n+\n+    setupSignalHandlers() {\n+        // Manejar Ctrl+C de forma más robusta\n+        process.on('SIGINT', () => {\n+            if (!this.gracefulShutdown) {\n+                console.log('\\n🛑 Ctrl+C detectado - Iniciando cierre seguro...');\n+                this.gracefulShutdown = true;\n+                this.stop().then(() => {\n+                    console.log('✅ Cierre completado');\n+                    process.exit(0);\n+                }).catch(() => {\n+                    console.log('⚠️ Forzando cierre...');\n+                    process.exit(1);\n+                });\n+\n+                // Timeout de seguridad\n+                setTimeout(() => {\n+                    console.log('⚠️ Timeout de cierre - Forzando salida');\n+                    process.exit(1);\n+                }, 5000);\n+            }\n+        });\n+\n+        process.on('SIGTERM', () => {\n+            console.log('\\n🛑 SIGTERM recibido...');\n+            this.stop().then(() => process.exit(0));\n+        });\n+\n+        // Manejar errores no capturados\n+        process.on('unhandledRejection', (reason) => {\n+            console.error('❌ Promesa rechazada:', reason);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+\n+        process.on('uncaughtException', (error) => {\n+            console.error('❌ Excepción no capturada:', error);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+    }\n+\n+    async init() {\n+        console.log('🚀 Zebra FX9600 RFID Tester - Iniciando...');\n+\n+        try {\n+            // Cargar configuración\n+            await this.loadConfig();\n+\n+            // Cargar ROSpec\n+            await this.loadROSpec();\n+\n+            // Configurar archivo de salida\n+            this.setupOutput();\n+\n+            // Cargar librería LLRP\n+            this.loadLLRPLibrary();\n+\n+            console.log('✅ Inicialización completada');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error en inicialización:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    loadLLRPLibrary() {\n+        try {\n+            console.log('📦 Cargando librería llrpjs...');\n+            const llrpjs = require('llrpjs');\n+            this.LLRPClient = llrpjs.LLRPClient;\n+            this.LLRPCore = llrpjs.LLRPCore;\n+            console.log('✅ Librería llrpjs cargada correctamente');\n+        } catch (error) {\n+            throw new Error(`No se pudo cargar llrpjs: ${error.message}`);\n+        }\n+    }\n+\n+    async loadConfig() {\n+        const configFile = this.options.config;\n+\n+        if (!fs.existsSync(configFile)) {\n+            console.log(`📝 Creando archivo de configuración: ${configFile}`);\n+            fs.writeFileSync(configFile, JSON.stringify(DEFAULT_CONFIG, null, 2));\n+        }\n+\n+        const configData = fs.readFileSync(configFile, 'utf8');\n+        this.config = JSON.parse(configData);\n+\n+        // Override con opciones de línea de comandos\n+        if (this.options.host) this.config.host = this.options.host;\n+        if (this.options.port) this.config.port = parseInt(this.options.port);\n+\n+        console.log('📋 Configuración cargada:');\n+        console.log(`   Host: ${this.config.host}`);\n+        console.log(`   Puerto: ${this.config.port}`);\n+        console.log(`   Timeout: ${this.config.timeout}ms`);\n+    }\n+\n+    async loadROSpec() {\n+        const rospecFile = this.options.rospec;\n+\n+        if (!fs.existsSync(rospecFile)) {\n+            console.log(`📝 Creando archivo ROSpec por defecto: ${rospecFile}`);\n+            const defaultROSpec = this.createDefaultROSpec();\n+            fs.writeFileSync(rospecFile, JSON.stringify(defaultROSpec, null, 2));\n+        }\n+\n+        const rospecData = fs.readFileSync(rospecFile, 'utf8');\n+        this.rospecConfig = JSON.parse(rospecData);\n+\n+        console.log('📡 ROSpec cargado:');\n+        console.log(`   ROSpec ID: ${this.rospecConfig.data.ROSpec.ROSpecID}`);\n+        console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);\n+    }\n+\n+    createDefaultROSpec() {\n+        // ROSpec basado en la configuración real del Zebra que funciona\n+        return {\n+            \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n+            \"id\": 103,\n+            \"type\": \"ADD_ROSPEC\",\n+            \"data\": {\n+                \"ROSpec\": {\n+                    \"ROSpecID\": 1,\n+                    \"Priority\": 0,\n+                    \"CurrentState\": \"Disabled\",\n+                    \"ROBoundarySpec\": {\n+                        \"ROSpecStartTrigger\": {\n+                            \"ROSpecStartTriggerType\": \"Immediate\"\n+                        },\n+                        \"ROSpecStopTrigger\": {\n+                            \"ROSpecStopTriggerType\": \"Immediate\"\n+                        }\n+                    },\n+                    \"AISpec\": {\n+                        \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8], // Todas las 8 antenas como en la config real\n+                        \"AISpecStopTrigger\": {\n+                            \"AISpecStopTriggerType\": \"Duration\",\n+                            \"DurationTriggerValue\": 400 // 400ms como en la config real\n+                        },\n+                        \"InventoryParameterSpec\": {\n+                            \"InventoryParameterSpecID\": 1,\n+                            \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n+                        }\n+                    },\n+                    \"ROReportSpec\": {\n+                        \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n+                        \"N\": 1, // Tag Report Trigger: 1 como en la config real\n+                        \"TagReportContentSelector\": {\n+                            \"EnableROSpecID\": false,\n+                            \"EnableSpecIndex\": false,\n+                            \"EnableInventoryParameterSpecID\": false,\n+                            \"EnableAntennaID\": true,\n+                            \"EnableChannelIndex\": false,\n+                            \"EnablePeakRSSI\": true,\n+                            \"EnableFirstSeenTimestamp\": true,\n+                            \"EnableLastSeenTimestamp\": true,\n+                            \"EnableTagSeenCount\": true,\n+                            \"EnableAccessSpecID\": false\n+                        }\n+                    }\n+                }\n+            }\n+        };\n+    }\n+\n+    setupOutput() {\n+        const outputFile = this.options.output;\n+        console.log(`📄 Archivo de salida: ${outputFile}`);\n+\n+        // Crear stream de escritura\n+        this.outputStream = fs.createWriteStream(outputFile, { flags: 'a' });\n+\n+        // Escribir header\n+        const header = `\\n=== SESIÓN INICIADA: ${new Date().toISOString()} ===\\n`;\n+        this.outputStream.write(header);\n+        console.log(`📝 Logs se guardarán en: ${outputFile}`);\n+    }\n+\n+    async connect() {\n+        try {\n+            console.log('🔗 Conectando al reader...');\n+\n+            // Crear cliente LLRP\n+            this.connection = new this.LLRPClient({\n+                host: this.config.host,\n+                port: this.config.port\n+            });\n+\n+            // Configurar event handlers\n+            this.setupEventHandlers();\n+\n+            // Conectar con timeout\n+            await Promise.race([\n+                this.connection.connect(),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout de conexión')), this.config.timeout)\n+                )\n+            ]);\n+\n+            this.isConnected = true;\n+            this.reconnectAttempts = 0;\n+            console.log('✅ Conectado al reader Zebra FX9600');\n+\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error de conexión:', error.message);\n+            this.isConnected = false;\n+            return false;\n+        }\n+    }\n+\n+    setupEventHandlers() {\n+        const that = this;\n+\n+        this.connection.on('error', function(error) {\n+            console.error('❌ ERROR DE CONEXIÓN:', error.message);\n+            that.isConnected = false;\n+            if (that.isRunning && !that.gracefulShutdown) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        this.connection.on('connect', function() {\n+            console.log('🔗 Evento CONNECT recibido');\n+        });\n+\n+        this.connection.on('disconnect', function() {\n+            console.log('🔌 Evento DISCONNECT recibido');\n+            that.isConnected = false;\n+            if (that.isRunning) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        // Handler para reportes de tags RFID\n+        this.connection.on('RO_ACCESS_REPORT', function(msg) {\n+            that.processTagReport(msg);\n+        });\n+\n+        // Handler para notificaciones de eventos del reader\n+        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {\n+            console.log('📢 READER_EVENT_NOTIFICATION recibido');\n+        });\n+    }\n+\n+    processTagReport(msg) {\n+        try {\n+            const tagReportData = msg.getTagReportData ? msg.getTagReportData() : [];\n+            const tags = Array.isArray(tagReportData) ? tagReportData : [tagReportData];\n+\n+            for (const tag of tags) {\n+                if (tag && tag.getEPCParameter) {\n+                    const epcParam = tag.getEPCParameter();\n+                    const epc = epcParam ? epcParam.getEPC() : null;\n+                    const antennaId = tag.getAntennaID ? tag.getAntennaID() : 'Unknown';\n+                    const rssi = tag.getPeakRSSI ? tag.getPeakRSSI() : null;\n+                    const timestamp = new Date().toISOString();\n+\n+                    if (epc) {\n+                        this.logTag(epc, antennaId, rssi, timestamp);\n+                    }\n+                }\n+            }\n+        } catch (error) {\n+            console.error('❌ Error procesando reporte de tags:', error.message);\n+        }\n+    }\n+\n+    logTag(epc, antenna, rssi, timestamp) {\n+        this.stats.totalTags++;\n+        this.stats.uniqueTags.add(epc);\n+        this.stats.lastTagTime = timestamp;\n+\n+        if (!this.stats.antennaStats[antenna]) {\n+            this.stats.antennaStats[antenna] = 0;\n+        }\n+        this.stats.antennaStats[antenna]++;\n+\n+        // Log a consola\n+        const logMessage = `📡 TAG: ${epc} | Antena: ${antenna} | RSSI: ${rssi} | ${timestamp}`;\n+        console.log(logMessage);\n+\n+        // Log a archivo\n+        const fileMessage = `${timestamp},${epc},${antenna},${rssi}\\n`;\n+        this.outputStream.write(fileMessage);\n+\n+        // Mostrar estadísticas cada 10 tags\n+        if (this.stats.totalTags % 10 === 0) {\n+            this.showStats();\n+        }\n+    }\n+\n+    showStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS:');\n+        console.log(`   Total tags: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo ejecución: ${runtime}s`);\n+        console.log(`   Tags por antena:`, this.stats.antennaStats);\n+        console.log('');\n+    }\n+\n+    async initializeReader() {\n+        try {\n+            console.log('🔧 Inicializando reader...');\n+\n+            // Esperar confirmación de conexión\n+            await this.waitForConnectionConfirmation();\n+\n+            // Limpiar ROSpecs existentes\n+            await this.cleanupROSpecs();\n+\n+            // Crear y configurar ROSpec\n+            await this.setupROSpec();\n+\n+            console.log('✅ Reader inicializado correctamente');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error inicializando reader:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    async waitForConnectionConfirmation() {\n+        console.log('⏳ Esperando confirmación de conexión...');\n+\n+        return new Promise((resolve, reject) => {\n+            const timeout = setTimeout(() => {\n+                reject(new Error('Timeout esperando confirmación'));\n+            }, this.config.timeout);\n+\n+            const checkConnection = () => {\n+                if (this.isConnected) {\n+                    clearTimeout(timeout);\n+                    resolve();\n+                } else {\n+                    setTimeout(checkConnection, 100);\n+                }\n+            };\n+\n+            checkConnection();\n+        });\n+    }\n+\n+    async cleanupROSpecs() {\n+        try {\n+            console.log('🗑️ Limpiando ROSpecs existentes...');\n+\n+            // Obtener ROSpecs existentes\n+            const response = await Promise.race([\n+                this.connection.transact(new this.LLRPCore.GET_ROSPECS()),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)\n+                )\n+            ]);\n+\n+            // Procesar ROSpecs\n+            let rospecs = response.getROSpec ? response.getROSpec() : [];\n+            if (!Array.isArray(rospecs)) {\n+                rospecs = rospecs ? [rospecs] : [];\n+            }\n+\n+            // Eliminar cada ROSpec\n+            for (const rospec of rospecs) {\n+                const rospecId = rospec.getROSpecID ? rospec.getROSpecID() : null;\n+                if (rospecId) {\n+                    try {\n+                        await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                            data: { ROSpecID: rospecId }\n+                        }));\n+                        console.log(`   ✅ ROSpec ${rospecId} eliminado`);\n+                    } catch (error) {\n+                        console.log(`   ⚠️ Error eliminando ROSpec ${rospecId}:`, error.message);\n+                    }\n+                }\n+            }\n+\n+        } catch (error) {\n+            console.log('⚠️ Error en limpieza de ROSpecs:', error.message);\n+        }\n+    }\n+\n+    async setupROSpec() {\n+        try {\n+            console.log('📡 Configurando ROSpec...');\n+\n+            // Crear ROSpec\n+            await this.connection.transact(new this.LLRPCore.ADD_ROSPEC(this.rospecConfig));\n+            console.log('✅ ROSpec creado');\n+\n+            // Habilitar ROSpec\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+            await this.connection.transact(new this.LLRPCore.ENABLE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec habilitado');\n+\n+            // Iniciar ROSpec\n+            await this.connection.transact(new this.LLRPCore.START_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec iniciado');\n+\n+            console.log('🎉 Reader listo para detectar tags');\n+\n+        } catch (error) {\n+            throw new Error(`Error configurando ROSpec: ${error.message}`);\n+        }\n+    }\n+\n+    async stopROSpec() {\n+        try {\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+\n+            console.log('🛑 Deteniendo ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.STOP_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('🗑️ Eliminando ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('✅ ROSpec detenido y eliminado');\n+\n+        } catch (error) {\n+            console.log('⚠️ Error deteniendo ROSpec:', error.message);\n+        }\n+    }\n+\n+    scheduleReconnect() {\n+        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {\n+            console.error('❌ Máximo número de intentos de reconexión alcanzado');\n+            this.stop();\n+            return;\n+        }\n+\n+        this.reconnectAttempts++;\n+        console.log(`🔄 Reintentando conexión en ${this.config.reconnectInterval}ms (intento ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);\n+\n+        setTimeout(async () => {\n+            if (this.isRunning) {\n+                const connected = await this.connect();\n+                if (connected) {\n+                    await this.initializeReader();\n+                }\n+            }\n+        }, this.config.reconnectInterval);\n+    }\n+\n+    async start() {\n+        console.log('🚀 Iniciando lectura de tags...');\n+        this.isRunning = true;\n+        this.stats.startTime = Date.now();\n+\n+        // Conectar al reader\n+        const connected = await this.connect();\n+        if (!connected) {\n+            console.error('❌ No se pudo conectar al reader');\n+            return false;\n+        }\n+\n+        // Inicializar reader\n+        const initialized = await this.initializeReader();\n+        if (!initialized) {\n+            console.error('❌ No se pudo inicializar el reader');\n+            return false;\n+        }\n+\n+        console.log('✅ Lectura de tags iniciada');\n+        console.log('📝 Presiona Ctrl+C para detener');\n+\n+        return true;\n+    }\n+\n+    async stop() {\n+        console.log('🛑 Deteniendo lectura de tags...');\n+        this.isRunning = false;\n+\n+        if (this.connection && this.isConnected) {\n+            await this.stopROSpec();\n+\n+            try {\n+                await this.connection.transact(new this.LLRPCore.CLOSE_CONNECTION());\n+                this.connection.disconnect();\n+            } catch (error) {\n+                console.log('⚠️ Error cerrando conexión:', error.message);\n+            }\n+        }\n+\n+        if (this.outputStream) {\n+            const footer = `=== SESIÓN FINALIZADA: ${new Date().toISOString()} ===\\n`;\n+            this.outputStream.write(footer);\n+            this.outputStream.end();\n+        }\n+\n+        this.showFinalStats();\n+        console.log('✅ Lectura detenida');\n+    }\n+\n+    showFinalStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS FINALES:');\n+        console.log(`   Total tags detectados: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo total: ${runtime}s`);\n+        console.log(`   Promedio: ${runtime > 0 ? (this.stats.totalTags / runtime).toFixed(2) : 0} tags/s`);\n+        console.log(`   Distribución por antena:`, this.stats.antennaStats);\n+        console.log(`   Último tag: ${this.stats.lastTagTime || 'N/A'}`);\n+        console.log('');\n+    }\n+}\n+\n+// Función para parsear argumentos de línea de comandos\n+function parseArgs() {\n+    const args = process.argv.slice(2);\n+    const options = {};\n+\n+    for (let i = 0; i < args.length; i++) {\n+        const arg = args[i];\n+\n+        switch (arg) {\n+            case '--help':\n+            case '-h':\n+                showHelp();\n+                process.exit(0);\n+                break;\n+            case '--config':\n+                options.config = args[++i];\n+                break;\n+            case '--rospec':\n+                options.rospec = args[++i];\n+                break;\n+            case '--output':\n+                options.output = args[++i];\n+                break;\n+            case '--host':\n+                options.host = args[++i];\n+                break;\n+            case '--port':\n+                options.port = args[++i];\n+                break;\n+            default:\n+                if (arg.startsWith('--')) {\n+                    console.error(`❌ Opción desconocida: ${arg}`);\n+                    showHelp();\n+                    process.exit(1);\n+                }\n+        }\n+    }\n+\n+    return options;\n+}\n+\n+function showHelp() {\n+    console.log(`\n+🦓 Zebra FX9600 RFID Reader Tester\n+\n+Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+\n+USO:\n+    node zebra-rfid-tester.js [opciones]\n+\n+OPCIONES:\n+    --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+    --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+    --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+    --host <ip>          IP del reader (override config)\n+    --port <puerto>      Puerto del reader (override config)\n+    --help, -h           Mostrar esta ayuda\n+\n+EJEMPLOS:\n+    # Usar configuración por defecto\n+    node zebra-rfid-tester.js\n+\n+    # Especificar IP y puerto\n+    node zebra-rfid-tester.js --host ************* --port 5084\n+\n+    # Usar archivos de configuración personalizados\n+    node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json\n+\n+    # Guardar logs en archivo específico\n+    node zebra-rfid-tester.js --output mis-tags.log\n+\n+ARCHIVOS GENERADOS:\n+    zebra-config.json     Configuración del reader (IP, puerto, timeouts)\n+    zebra-rospec.json     Configuración ROSpec (antenas, parámetros RFID)\n+    zebra-tags.log        Log de tags detectados (CSV format)\n+\n+FORMATO DEL LOG:\n+    timestamp,epc,antenna,rssi\n+    2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45\n+\n+MONITOREO EN TIEMPO REAL:\n+    # En otra terminal, para ver tags en tiempo real:\n+    tail -f zebra-tags.log\n+\n+    # Para ver solo los EPCs:\n+    tail -f zebra-tags.log | cut -d',' -f2\n+\n+    # Para contar tags únicos:\n+    tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l\n+`);\n+}\n+\n+// Función principal\n+async function main() {\n+    console.log('🦓 Zebra FX9600 RFID Reader Tester v1.0');\n+    console.log('=====================================\\n');\n+\n+    // Parsear argumentos\n+    const options = parseArgs();\n+\n+    // Crear instancia del tester\n+    const tester = new ZebraRFIDTester(options);\n+\n+    // Manejar señales de interrupción\n+    process.on('SIGINT', async () => {\n+        console.log('\\n🛑 Señal de interrupción recibida...');\n+        await tester.stop();\n+        process.exit(0);\n+    });\n+\n+    process.on('SIGTERM', async () => {\n+        console.log('\\n🛑 Señal de terminación recibida...');\n+        await tester.stop();\n+        process.exit(0);\n+    });\n+\n+    // Manejar errores no capturados\n+    process.on('unhandledRejection', (reason, promise) => {\n+        console.error('❌ Promesa rechazada no manejada:', reason);\n+        tester.stop().then(() => process.exit(1));\n+    });\n+\n+    process.on('uncaughtException', (error) => {\n+        console.error('❌ Excepción no capturada:', error);\n+        tester.stop().then(() => process.exit(1));\n+    });\n+\n+    try {\n+        // Inicializar\n+        const initialized = await tester.init();\n+        if (!initialized) {\n+            console.error('❌ Error en inicialización');\n+            process.exit(1);\n+        }\n+\n+        // Iniciar lectura\n+        const started = await tester.start();\n+        if (!started) {\n+            console.error('❌ Error iniciando lectura');\n+            process.exit(1);\n+        }\n+\n+        // Mantener el proceso vivo\n+        console.log('🔄 Leyendo tags... (Ctrl+C para detener)');\n+\n+        // Mostrar estadísticas cada 30 segundos\n+        setInterval(() => {\n+            if (tester.isRunning) {\n+                tester.showStats();\n+            }\n+        }, 30000);\n+\n+    } catch (error) {\n+        console.error('❌ Error fatal:', error.message);\n+        await tester.stop();\n+        process.exit(1);\n+    }\n+}\n+\n+// Ejecutar solo si es el archivo principal\n+if (require.main === module) {\n+    main().catch(error => {\n+        console.error('❌ Error en main:', error);\n+        process.exit(1);\n+    });\n+}\n+\n+module.exports = ZebraRFIDTester;\n"}, {"date": 1758058294938, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,755 @@\n+#!/usr/bin/env node\n+\n+/**\n+ * Zebra FX9600 RFID Reader Tester\n+ * Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+ *\n+ * Uso:\n+ *   node zebra-rfid-tester.js [opciones]\n+ *\n+ * Opciones:\n+ *   --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+ *   --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+ *   --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+ *   --host <ip>          IP del reader (override config)\n+ *   --port <puerto>      Puerto del reader (override config)\n+ *   --help               <PERSON>rar ayuda\n+ */\n+\n+const fs = require('fs');\n+const path = require('path');\n+\n+// Configuración por defecto\n+const DEFAULT_CONFIG = {\n+    host: '*************',\n+    port: 5084,\n+    timeout: 10000,\n+    reconnectInterval: 5000,\n+    maxReconnectAttempts: 5\n+};\n+\n+const DEFAULT_FILES = {\n+    config: 'zebra-config.json',\n+    rospec: 'zebra-rospec.json',\n+    output: 'zebra-tags.log'\n+};\n+\n+class ZebraRFIDTester {\n+    constructor(options = {}) {\n+        this.options = { ...DEFAULT_FILES, ...options };\n+        this.config = null;\n+        this.rospecConfig = null;\n+        this.connection = null;\n+        this.isConnected = false;\n+        this.isRunning = false;\n+        this.reconnectAttempts = 0;\n+        this.tagCount = 0;\n+        this.startTime = null;\n+        this.outputStream = null;\n+        this.gracefulShutdown = false;\n+\n+        // Estadísticas\n+        this.stats = {\n+            totalTags: 0,\n+            uniqueTags: new Set(),\n+            antennaStats: {},\n+            lastTagTime: null,\n+            startTime: null\n+        };\n+\n+        // Configurar manejo de señales mejorado\n+        this.setupSignalHandlers();\n+    }\n+\n+    setupSignalHandlers() {\n+        // Manejar Ctrl+C de forma más robusta\n+        process.on('SIGINT', () => {\n+            if (!this.gracefulShutdown) {\n+                console.log('\\n🛑 Ctrl+C detectado - Iniciando cierre seguro...');\n+                this.gracefulShutdown = true;\n+                this.stop().then(() => {\n+                    console.log('✅ Cierre completado');\n+                    process.exit(0);\n+                }).catch(() => {\n+                    console.log('⚠️ Forzando cierre...');\n+                    process.exit(1);\n+                });\n+\n+                // Timeout de seguridad\n+                setTimeout(() => {\n+                    console.log('⚠️ Timeout de cierre - Forzando salida');\n+                    process.exit(1);\n+                }, 5000);\n+            }\n+        });\n+\n+        process.on('SIGTERM', () => {\n+            console.log('\\n🛑 SIGTERM recibido...');\n+            this.stop().then(() => process.exit(0));\n+        });\n+\n+        // Manejar errores no capturados\n+        process.on('unhandledRejection', (reason) => {\n+            console.error('❌ Promesa rechazada:', reason);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+\n+        process.on('uncaughtException', (error) => {\n+            console.error('❌ Excepción no capturada:', error);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+    }\n+\n+    async init() {\n+        console.log('🚀 Zebra FX9600 RFID Tester - Iniciando...');\n+\n+        try {\n+            // Cargar configuración\n+            await this.loadConfig();\n+\n+            // Cargar ROSpec\n+            await this.loadROSpec();\n+\n+            // Configurar archivo de salida\n+            this.setupOutput();\n+\n+            // Cargar librería LLRP\n+            this.loadLLRPLibrary();\n+\n+            console.log('✅ Inicialización completada');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error en inicialización:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    loadLLRPLibrary() {\n+        try {\n+            console.log('📦 Cargando librería llrpjs...');\n+            const llrpjs = require('llrpjs');\n+            this.LLRPClient = llrpjs.LLRPClient;\n+            this.LLRPCore = llrpjs.LLRPCore;\n+            console.log('✅ Librería llrpjs cargada correctamente');\n+        } catch (error) {\n+            throw new Error(`No se pudo cargar llrpjs: ${error.message}`);\n+        }\n+    }\n+\n+    async loadConfig() {\n+        const configFile = this.options.config;\n+\n+        if (!fs.existsSync(configFile)) {\n+            console.log(`📝 Creando archivo de configuración: ${configFile}`);\n+            fs.writeFileSync(configFile, JSON.stringify(DEFAULT_CONFIG, null, 2));\n+        }\n+\n+        const configData = fs.readFileSync(configFile, 'utf8');\n+        this.config = JSON.parse(configData);\n+\n+        // Override con opciones de línea de comandos\n+        if (this.options.host) this.config.host = this.options.host;\n+        if (this.options.port) this.config.port = parseInt(this.options.port);\n+\n+        console.log('📋 Configuración cargada:');\n+        console.log(`   Host: ${this.config.host}`);\n+        console.log(`   Puerto: ${this.config.port}`);\n+        console.log(`   Timeout: ${this.config.timeout}ms`);\n+    }\n+\n+    async loadROSpec() {\n+        const rospecFile = this.options.rospec;\n+\n+        if (!fs.existsSync(rospecFile)) {\n+            console.log(`📝 Creando archivo ROSpec por defecto: ${rospecFile}`);\n+            const defaultROSpec = this.createDefaultROSpec();\n+            fs.writeFileSync(rospecFile, JSON.stringify(defaultROSpec, null, 2));\n+        }\n+\n+        const rospecData = fs.readFileSync(rospecFile, 'utf8');\n+        this.rospecConfig = JSON.parse(rospecData);\n+\n+        console.log('📡 ROSpec cargado:');\n+        console.log(`   ROSpec ID: ${this.rospecConfig.data.ROSpec.ROSpecID}`);\n+        console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);\n+    }\n+\n+    createDefaultROSpec() {\n+        // ROSpec basado en la configuración real del Zebra que funciona\n+        return {\n+            \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n+            \"id\": 103,\n+            \"type\": \"ADD_ROSPEC\",\n+            \"data\": {\n+                \"ROSpec\": {\n+                    \"ROSpecID\": 1,\n+                    \"Priority\": 0,\n+                    \"CurrentState\": \"Disabled\",\n+                    \"ROBoundarySpec\": {\n+                        \"ROSpecStartTrigger\": {\n+                            \"ROSpecStartTriggerType\": \"Immediate\"\n+                        },\n+                        \"ROSpecStopTrigger\": {\n+                            \"ROSpecStopTriggerType\": \"Immediate\"\n+                        }\n+                    },\n+                    \"AISpec\": {\n+                        \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8], // Todas las 8 antenas como en la config real\n+                        \"AISpecStopTrigger\": {\n+                            \"AISpecStopTriggerType\": \"Duration\",\n+                            \"DurationTriggerValue\": 400 // 400ms como en la config real\n+                        },\n+                        \"InventoryParameterSpec\": {\n+                            \"InventoryParameterSpecID\": 1,\n+                            \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n+                        }\n+                    },\n+                    \"ROReportSpec\": {\n+                        \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n+                        \"N\": 1, // Tag Report Trigger: 1 como en la config real\n+                        \"TagReportContentSelector\": {\n+                            \"EnableROSpecID\": false,\n+                            \"EnableSpecIndex\": false,\n+                            \"EnableInventoryParameterSpecID\": false,\n+                            \"EnableAntennaID\": true,\n+                            \"EnableChannelIndex\": false,\n+                            \"EnablePeakRSSI\": true,\n+                            \"EnableFirstSeenTimestamp\": true,\n+                            \"EnableLastSeenTimestamp\": true,\n+                            \"EnableTagSeenCount\": true,\n+                            \"EnableAccessSpecID\": false\n+                        }\n+                    }\n+                }\n+            }\n+        };\n+    }\n+\n+    setupOutput() {\n+        const outputFile = this.options.output;\n+        console.log(`📄 Archivo de salida: ${outputFile}`);\n+\n+        // Crear stream de escritura\n+        this.outputStream = fs.createWriteStream(outputFile, { flags: 'a' });\n+\n+        // Escribir header\n+        const header = `\\n=== SESIÓN INICIADA: ${new Date().toISOString()} ===\\n`;\n+        this.outputStream.write(header);\n+        console.log(`📝 Logs se guardarán en: ${outputFile}`);\n+    }\n+\n+    async connect() {\n+        try {\n+            console.log('🔗 Conectando al reader...');\n+\n+            // Crear cliente LLRP\n+            this.connection = new this.LLRPClient({\n+                host: this.config.host,\n+                port: this.config.port\n+            });\n+\n+            // Configurar event handlers\n+            this.setupEventHandlers();\n+\n+            // Conectar con timeout\n+            await Promise.race([\n+                this.connection.connect(),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout de conexión')), this.config.timeout)\n+                )\n+            ]);\n+\n+            this.isConnected = true;\n+            this.reconnectAttempts = 0;\n+            console.log('✅ Conectado al reader Zebra FX9600');\n+\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error de conexión:', error.message);\n+            this.isConnected = false;\n+            return false;\n+        }\n+    }\n+\n+    setupEventHandlers() {\n+        const that = this;\n+\n+        this.connection.on('error', function(error) {\n+            console.error('❌ ERROR DE CONEXIÓN:', error.message);\n+            that.isConnected = false;\n+            if (that.isRunning && !that.gracefulShutdown) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        this.connection.on('connect', function() {\n+            console.log('🔗 Evento CONNECT recibido');\n+        });\n+\n+        this.connection.on('disconnect', function() {\n+            console.log('🔌 Evento DISCONNECT recibido');\n+            that.isConnected = false;\n+            if (that.isRunning && !that.gracefulShutdown) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        // Handler para reportes de tags RFID\n+        this.connection.on('RO_ACCESS_REPORT', function(msg) {\n+            that.processTagReport(msg);\n+        });\n+\n+        // Handler para notificaciones de eventos del reader\n+        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {\n+            console.log('📢 READER_EVENT_NOTIFICATION recibido');\n+        });\n+    }\n+\n+    processTagReport(msg) {\n+        try {\n+            const tagReportData = msg.getTagReportData ? msg.getTagReportData() : [];\n+            const tags = Array.isArray(tagReportData) ? tagReportData : [tagReportData];\n+\n+            for (const tag of tags) {\n+                if (tag && tag.getEPCParameter) {\n+                    const epcParam = tag.getEPCParameter();\n+                    const epc = epcParam ? epcParam.getEPC() : null;\n+                    const antennaId = tag.getAntennaID ? tag.getAntennaID() : 'Unknown';\n+                    const rssi = tag.getPeakRSSI ? tag.getPeakRSSI() : null;\n+                    const timestamp = new Date().toISOString();\n+\n+                    if (epc) {\n+                        this.logTag(epc, antennaId, rssi, timestamp);\n+                    }\n+                }\n+            }\n+        } catch (error) {\n+            console.error('❌ Error procesando reporte de tags:', error.message);\n+        }\n+    }\n+\n+    logTag(epc, antenna, rssi, timestamp) {\n+        this.stats.totalTags++;\n+        this.stats.uniqueTags.add(epc);\n+        this.stats.lastTagTime = timestamp;\n+\n+        if (!this.stats.antennaStats[antenna]) {\n+            this.stats.antennaStats[antenna] = 0;\n+        }\n+        this.stats.antennaStats[antenna]++;\n+\n+        // Log a consola\n+        const logMessage = `📡 TAG: ${epc} | Antena: ${antenna} | RSSI: ${rssi} | ${timestamp}`;\n+        console.log(logMessage);\n+\n+        // Log a archivo\n+        const fileMessage = `${timestamp},${epc},${antenna},${rssi}\\n`;\n+        this.outputStream.write(fileMessage);\n+\n+        // Mostrar estadísticas cada 10 tags\n+        if (this.stats.totalTags % 10 === 0) {\n+            this.showStats();\n+        }\n+    }\n+\n+    showStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS:');\n+        console.log(`   Total tags: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo ejecución: ${runtime}s`);\n+        console.log(`   Tags por antena:`, this.stats.antennaStats);\n+        console.log('');\n+    }\n+\n+    async initializeReader() {\n+        try {\n+            console.log('🔧 Inicializando reader...');\n+\n+            // Esperar confirmación de conexión\n+            await this.waitForConnectionConfirmation();\n+\n+            // Limpiar ROSpecs existentes\n+            await this.cleanupROSpecs();\n+\n+            // Crear y configurar ROSpec\n+            await this.setupROSpec();\n+\n+            console.log('✅ Reader inicializado correctamente');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error inicializando reader:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    async waitForConnectionConfirmation() {\n+        console.log('⏳ Esperando confirmación de conexión...');\n+\n+        return new Promise((resolve, reject) => {\n+            const timeout = setTimeout(() => {\n+                reject(new Error('Timeout esperando confirmación'));\n+            }, this.config.timeout);\n+\n+            const checkConnection = () => {\n+                if (this.isConnected) {\n+                    clearTimeout(timeout);\n+                    resolve();\n+                } else {\n+                    setTimeout(checkConnection, 100);\n+                }\n+            };\n+\n+            checkConnection();\n+        });\n+    }\n+\n+    async cleanupROSpecs() {\n+        try {\n+            console.log('🗑️ Limpiando ROSpecs existentes...');\n+\n+            // Obtener ROSpecs existentes\n+            const response = await Promise.race([\n+                this.connection.transact(new this.LLRPCore.GET_ROSPECS()),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)\n+                )\n+            ]);\n+\n+            // Procesar ROSpecs\n+            let rospecs = response.getROSpec ? response.getROSpec() : [];\n+            if (!Array.isArray(rospecs)) {\n+                rospecs = rospecs ? [rospecs] : [];\n+            }\n+\n+            // Eliminar cada ROSpec\n+            for (const rospec of rospecs) {\n+                const rospecId = rospec.getROSpecID ? rospec.getROSpecID() : null;\n+                if (rospecId) {\n+                    try {\n+                        await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                            data: { ROSpecID: rospecId }\n+                        }));\n+                        console.log(`   ✅ ROSpec ${rospecId} eliminado`);\n+                    } catch (error) {\n+                        console.log(`   ⚠️ Error eliminando ROSpec ${rospecId}:`, error.message);\n+                    }\n+                }\n+            }\n+\n+        } catch (error) {\n+            console.log('⚠️ Error en limpieza de ROSpecs:', error.message);\n+        }\n+    }\n+\n+    async setupROSpec() {\n+        try {\n+            console.log('📡 Configurando ROSpec...');\n+\n+            // Crear ROSpec\n+            await this.connection.transact(new this.LLRPCore.ADD_ROSPEC(this.rospecConfig));\n+            console.log('✅ ROSpec creado');\n+\n+            // Habilitar ROSpec\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+            await this.connection.transact(new this.LLRPCore.ENABLE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec habilitado');\n+\n+            // Iniciar ROSpec\n+            await this.connection.transact(new this.LLRPCore.START_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec iniciado');\n+\n+            console.log('🎉 Reader listo para detectar tags');\n+\n+        } catch (error) {\n+            throw new Error(`Error configurando ROSpec: ${error.message}`);\n+        }\n+    }\n+\n+    async stopROSpec() {\n+        try {\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+\n+            console.log('🛑 Deteniendo ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.STOP_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('🗑️ Eliminando ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('✅ ROSpec detenido y eliminado');\n+\n+        } catch (error) {\n+            console.log('⚠️ Error deteniendo ROSpec:', error.message);\n+        }\n+    }\n+\n+    scheduleReconnect() {\n+        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {\n+            console.error('❌ Máximo número de intentos de reconexión alcanzado');\n+            this.stop();\n+            return;\n+        }\n+\n+        this.reconnectAttempts++;\n+        console.log(`🔄 Reintentando conexión en ${this.config.reconnectInterval}ms (intento ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);\n+\n+        setTimeout(async () => {\n+            if (this.isRunning) {\n+                const connected = await this.connect();\n+                if (connected) {\n+                    await this.initializeReader();\n+                }\n+            }\n+        }, this.config.reconnectInterval);\n+    }\n+\n+    async start() {\n+        console.log('🚀 Iniciando lectura de tags...');\n+        this.isRunning = true;\n+        this.stats.startTime = Date.now();\n+\n+        // Conectar al reader\n+        const connected = await this.connect();\n+        if (!connected) {\n+            console.error('❌ No se pudo conectar al reader');\n+            return false;\n+        }\n+\n+        // Inicializar reader\n+        const initialized = await this.initializeReader();\n+        if (!initialized) {\n+            console.error('❌ No se pudo inicializar el reader');\n+            return false;\n+        }\n+\n+        console.log('✅ Lectura de tags iniciada');\n+        console.log('📝 Presiona Ctrl+C para detener');\n+\n+        return true;\n+    }\n+\n+    async stop() {\n+        console.log('🛑 Deteniendo lectura de tags...');\n+        this.isRunning = false;\n+\n+        if (this.connection && this.isConnected) {\n+            await this.stopROSpec();\n+\n+            try {\n+                await this.connection.transact(new this.LLRPCore.CLOSE_CONNECTION());\n+                this.connection.disconnect();\n+            } catch (error) {\n+                console.log('⚠️ Error cerrando conexión:', error.message);\n+            }\n+        }\n+\n+        if (this.outputStream) {\n+            const footer = `=== SESIÓN FINALIZADA: ${new Date().toISOString()} ===\\n`;\n+            this.outputStream.write(footer);\n+            this.outputStream.end();\n+        }\n+\n+        this.showFinalStats();\n+        console.log('✅ Lectura detenida');\n+    }\n+\n+    showFinalStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS FINALES:');\n+        console.log(`   Total tags detectados: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo total: ${runtime}s`);\n+        console.log(`   Promedio: ${runtime > 0 ? (this.stats.totalTags / runtime).toFixed(2) : 0} tags/s`);\n+        console.log(`   Distribución por antena:`, this.stats.antennaStats);\n+        console.log(`   Último tag: ${this.stats.lastTagTime || 'N/A'}`);\n+        console.log('');\n+    }\n+}\n+\n+// Función para parsear argumentos de línea de comandos\n+function parseArgs() {\n+    const args = process.argv.slice(2);\n+    const options = {};\n+\n+    for (let i = 0; i < args.length; i++) {\n+        const arg = args[i];\n+\n+        switch (arg) {\n+            case '--help':\n+            case '-h':\n+                showHelp();\n+                process.exit(0);\n+                break;\n+            case '--config':\n+                options.config = args[++i];\n+                break;\n+            case '--rospec':\n+                options.rospec = args[++i];\n+                break;\n+            case '--output':\n+                options.output = args[++i];\n+                break;\n+            case '--host':\n+                options.host = args[++i];\n+                break;\n+            case '--port':\n+                options.port = args[++i];\n+                break;\n+            default:\n+                if (arg.startsWith('--')) {\n+                    console.error(`❌ Opción desconocida: ${arg}`);\n+                    showHelp();\n+                    process.exit(1);\n+                }\n+        }\n+    }\n+\n+    return options;\n+}\n+\n+function showHelp() {\n+    console.log(`\n+🦓 Zebra FX9600 RFID Reader Tester\n+\n+Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+\n+USO:\n+    node zebra-rfid-tester.js [opciones]\n+\n+OPCIONES:\n+    --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+    --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+    --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+    --host <ip>          IP del reader (override config)\n+    --port <puerto>      Puerto del reader (override config)\n+    --help, -h           Mostrar esta ayuda\n+\n+EJEMPLOS:\n+    # Usar configuración por defecto\n+    node zebra-rfid-tester.js\n+\n+    # Especificar IP y puerto\n+    node zebra-rfid-tester.js --host ************* --port 5084\n+\n+    # Usar archivos de configuración personalizados\n+    node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json\n+\n+    # Guardar logs en archivo específico\n+    node zebra-rfid-tester.js --output mis-tags.log\n+\n+ARCHIVOS GENERADOS:\n+    zebra-config.json     Configuración del reader (IP, puerto, timeouts)\n+    zebra-rospec.json     Configuración ROSpec (antenas, parámetros RFID)\n+    zebra-tags.log        Log de tags detectados (CSV format)\n+\n+FORMATO DEL LOG:\n+    timestamp,epc,antenna,rssi\n+    2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45\n+\n+MONITOREO EN TIEMPO REAL:\n+    # En otra terminal, para ver tags en tiempo real:\n+    tail -f zebra-tags.log\n+\n+    # Para ver solo los EPCs:\n+    tail -f zebra-tags.log | cut -d',' -f2\n+\n+    # Para contar tags únicos:\n+    tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l\n+`);\n+}\n+\n+// Función principal\n+async function main() {\n+    console.log('🦓 Zebra FX9600 RFID Reader Tester v1.0');\n+    console.log('=====================================\\n');\n+\n+    // Parsear argumentos\n+    const options = parseArgs();\n+\n+    // Crear instancia del tester\n+    const tester = new ZebraRFIDTester(options);\n+\n+    // Manejar señales de interrupción\n+    process.on('SIGINT', async () => {\n+        console.log('\\n🛑 Señal de interrupción recibida...');\n+        await tester.stop();\n+        process.exit(0);\n+    });\n+\n+    process.on('SIGTERM', async () => {\n+        console.log('\\n🛑 Señal de terminación recibida...');\n+        await tester.stop();\n+        process.exit(0);\n+    });\n+\n+    // Manejar errores no capturados\n+    process.on('unhandledRejection', (reason, promise) => {\n+        console.error('❌ Promesa rechazada no manejada:', reason);\n+        tester.stop().then(() => process.exit(1));\n+    });\n+\n+    process.on('uncaughtException', (error) => {\n+        console.error('❌ Excepción no capturada:', error);\n+        tester.stop().then(() => process.exit(1));\n+    });\n+\n+    try {\n+        // Inicializar\n+        const initialized = await tester.init();\n+        if (!initialized) {\n+            console.error('❌ Error en inicialización');\n+            process.exit(1);\n+        }\n+\n+        // Iniciar lectura\n+        const started = await tester.start();\n+        if (!started) {\n+            console.error('❌ Error iniciando lectura');\n+            process.exit(1);\n+        }\n+\n+        // Mantener el proceso vivo\n+        console.log('🔄 Leyendo tags... (Ctrl+C para detener)');\n+\n+        // Mostrar estadísticas cada 30 segundos\n+        setInterval(() => {\n+            if (tester.isRunning) {\n+                tester.showStats();\n+            }\n+        }, 30000);\n+\n+    } catch (error) {\n+        console.error('❌ Error fatal:', error.message);\n+        await tester.stop();\n+        process.exit(1);\n+    }\n+}\n+\n+// Ejecutar solo si es el archivo principal\n+if (require.main === module) {\n+    main().catch(error => {\n+        console.error('❌ Error en main:', error);\n+        process.exit(1);\n+    });\n+}\n+\n+module.exports = ZebraRFIDTester;\n"}, {"date": 1758058309454, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,733 @@\n+#!/usr/bin/env node\n+\n+/**\n+ * Zebra FX9600 RFID Reader Tester\n+ * Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+ *\n+ * Uso:\n+ *   node zebra-rfid-tester.js [opciones]\n+ *\n+ * Opciones:\n+ *   --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+ *   --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+ *   --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+ *   --host <ip>          IP del reader (override config)\n+ *   --port <puerto>      Puerto del reader (override config)\n+ *   --help               <PERSON>rar ayuda\n+ */\n+\n+const fs = require('fs');\n+const path = require('path');\n+\n+// Configuración por defecto\n+const DEFAULT_CONFIG = {\n+    host: '*************',\n+    port: 5084,\n+    timeout: 10000,\n+    reconnectInterval: 5000,\n+    maxReconnectAttempts: 5\n+};\n+\n+const DEFAULT_FILES = {\n+    config: 'zebra-config.json',\n+    rospec: 'zebra-rospec.json',\n+    output: 'zebra-tags.log'\n+};\n+\n+class ZebraRFIDTester {\n+    constructor(options = {}) {\n+        this.options = { ...DEFAULT_FILES, ...options };\n+        this.config = null;\n+        this.rospecConfig = null;\n+        this.connection = null;\n+        this.isConnected = false;\n+        this.isRunning = false;\n+        this.reconnectAttempts = 0;\n+        this.tagCount = 0;\n+        this.startTime = null;\n+        this.outputStream = null;\n+        this.gracefulShutdown = false;\n+\n+        // Estadísticas\n+        this.stats = {\n+            totalTags: 0,\n+            uniqueTags: new Set(),\n+            antennaStats: {},\n+            lastTagTime: null,\n+            startTime: null\n+        };\n+\n+        // Configurar manejo de señales mejorado\n+        this.setupSignalHandlers();\n+    }\n+\n+    setupSignalHandlers() {\n+        // Manejar Ctrl+C de forma más robusta\n+        process.on('SIGINT', () => {\n+            if (!this.gracefulShutdown) {\n+                console.log('\\n🛑 Ctrl+C detectado - Iniciando cierre seguro...');\n+                this.gracefulShutdown = true;\n+                this.stop().then(() => {\n+                    console.log('✅ Cierre completado');\n+                    process.exit(0);\n+                }).catch(() => {\n+                    console.log('⚠️ Forzando cierre...');\n+                    process.exit(1);\n+                });\n+\n+                // Timeout de seguridad\n+                setTimeout(() => {\n+                    console.log('⚠️ Timeout de cierre - Forzando salida');\n+                    process.exit(1);\n+                }, 5000);\n+            }\n+        });\n+\n+        process.on('SIGTERM', () => {\n+            console.log('\\n🛑 SIGTERM recibido...');\n+            this.stop().then(() => process.exit(0));\n+        });\n+\n+        // Manejar errores no capturados\n+        process.on('unhandledRejection', (reason) => {\n+            console.error('❌ Promesa rechazada:', reason);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+\n+        process.on('uncaughtException', (error) => {\n+            console.error('❌ Excepción no capturada:', error);\n+            if (!this.gracefulShutdown) {\n+                this.stop().then(() => process.exit(1));\n+            }\n+        });\n+    }\n+\n+    async init() {\n+        console.log('🚀 Zebra FX9600 RFID Tester - Iniciando...');\n+\n+        try {\n+            // Cargar configuración\n+            await this.loadConfig();\n+\n+            // Cargar ROSpec\n+            await this.loadROSpec();\n+\n+            // Configurar archivo de salida\n+            this.setupOutput();\n+\n+            // Cargar librería LLRP\n+            this.loadLLRPLibrary();\n+\n+            console.log('✅ Inicialización completada');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error en inicialización:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    loadLLRPLibrary() {\n+        try {\n+            console.log('📦 Cargando librería llrpjs...');\n+            const llrpjs = require('llrpjs');\n+            this.LLRPClient = llrpjs.LLRPClient;\n+            this.LLRPCore = llrpjs.LLRPCore;\n+            console.log('✅ Librería llrpjs cargada correctamente');\n+        } catch (error) {\n+            throw new Error(`No se pudo cargar llrpjs: ${error.message}`);\n+        }\n+    }\n+\n+    async loadConfig() {\n+        const configFile = this.options.config;\n+\n+        if (!fs.existsSync(configFile)) {\n+            console.log(`📝 Creando archivo de configuración: ${configFile}`);\n+            fs.writeFileSync(configFile, JSON.stringify(DEFAULT_CONFIG, null, 2));\n+        }\n+\n+        const configData = fs.readFileSync(configFile, 'utf8');\n+        this.config = JSON.parse(configData);\n+\n+        // Override con opciones de línea de comandos\n+        if (this.options.host) this.config.host = this.options.host;\n+        if (this.options.port) this.config.port = parseInt(this.options.port);\n+\n+        console.log('📋 Configuración cargada:');\n+        console.log(`   Host: ${this.config.host}`);\n+        console.log(`   Puerto: ${this.config.port}`);\n+        console.log(`   Timeout: ${this.config.timeout}ms`);\n+    }\n+\n+    async loadROSpec() {\n+        const rospecFile = this.options.rospec;\n+\n+        if (!fs.existsSync(rospecFile)) {\n+            console.log(`📝 Creando archivo ROSpec por defecto: ${rospecFile}`);\n+            const defaultROSpec = this.createDefaultROSpec();\n+            fs.writeFileSync(rospecFile, JSON.stringify(defaultROSpec, null, 2));\n+        }\n+\n+        const rospecData = fs.readFileSync(rospecFile, 'utf8');\n+        this.rospecConfig = JSON.parse(rospecData);\n+\n+        console.log('📡 ROSpec cargado:');\n+        console.log(`   ROSpec ID: ${this.rospecConfig.data.ROSpec.ROSpecID}`);\n+        console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);\n+    }\n+\n+    createDefaultROSpec() {\n+        // ROSpec basado en la configuración real del Zebra que funciona\n+        return {\n+            \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n+            \"id\": 103,\n+            \"type\": \"ADD_ROSPEC\",\n+            \"data\": {\n+                \"ROSpec\": {\n+                    \"ROSpecID\": 1,\n+                    \"Priority\": 0,\n+                    \"CurrentState\": \"Disabled\",\n+                    \"ROBoundarySpec\": {\n+                        \"ROSpecStartTrigger\": {\n+                            \"ROSpecStartTriggerType\": \"Immediate\"\n+                        },\n+                        \"ROSpecStopTrigger\": {\n+                            \"ROSpecStopTriggerType\": \"Immediate\"\n+                        }\n+                    },\n+                    \"AISpec\": {\n+                        \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8], // Todas las 8 antenas como en la config real\n+                        \"AISpecStopTrigger\": {\n+                            \"AISpecStopTriggerType\": \"Duration\",\n+                            \"DurationTriggerValue\": 400 // 400ms como en la config real\n+                        },\n+                        \"InventoryParameterSpec\": {\n+                            \"InventoryParameterSpecID\": 1,\n+                            \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n+                        }\n+                    },\n+                    \"ROReportSpec\": {\n+                        \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n+                        \"N\": 1, // Tag Report Trigger: 1 como en la config real\n+                        \"TagReportContentSelector\": {\n+                            \"EnableROSpecID\": false,\n+                            \"EnableSpecIndex\": false,\n+                            \"EnableInventoryParameterSpecID\": false,\n+                            \"EnableAntennaID\": true,\n+                            \"EnableChannelIndex\": false,\n+                            \"EnablePeakRSSI\": true,\n+                            \"EnableFirstSeenTimestamp\": true,\n+                            \"EnableLastSeenTimestamp\": true,\n+                            \"EnableTagSeenCount\": true,\n+                            \"EnableAccessSpecID\": false\n+                        }\n+                    }\n+                }\n+            }\n+        };\n+    }\n+\n+    setupOutput() {\n+        const outputFile = this.options.output;\n+        console.log(`📄 Archivo de salida: ${outputFile}`);\n+\n+        // Crear stream de escritura\n+        this.outputStream = fs.createWriteStream(outputFile, { flags: 'a' });\n+\n+        // Escribir header\n+        const header = `\\n=== SESIÓN INICIADA: ${new Date().toISOString()} ===\\n`;\n+        this.outputStream.write(header);\n+        console.log(`📝 Logs se guardarán en: ${outputFile}`);\n+    }\n+\n+    async connect() {\n+        try {\n+            console.log('🔗 Conectando al reader...');\n+\n+            // Crear cliente LLRP\n+            this.connection = new this.LLRPClient({\n+                host: this.config.host,\n+                port: this.config.port\n+            });\n+\n+            // Configurar event handlers\n+            this.setupEventHandlers();\n+\n+            // Conectar con timeout\n+            await Promise.race([\n+                this.connection.connect(),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout de conexión')), this.config.timeout)\n+                )\n+            ]);\n+\n+            this.isConnected = true;\n+            this.reconnectAttempts = 0;\n+            console.log('✅ Conectado al reader Zebra FX9600');\n+\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error de conexión:', error.message);\n+            this.isConnected = false;\n+            return false;\n+        }\n+    }\n+\n+    setupEventHandlers() {\n+        const that = this;\n+\n+        this.connection.on('error', function(error) {\n+            console.error('❌ ERROR DE CONEXIÓN:', error.message);\n+            that.isConnected = false;\n+            if (that.isRunning && !that.gracefulShutdown) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        this.connection.on('connect', function() {\n+            console.log('🔗 Evento CONNECT recibido');\n+        });\n+\n+        this.connection.on('disconnect', function() {\n+            console.log('🔌 Evento DISCONNECT recibido');\n+            that.isConnected = false;\n+            if (that.isRunning && !that.gracefulShutdown) {\n+                that.scheduleReconnect();\n+            }\n+        });\n+\n+        // Handler para reportes de tags RFID\n+        this.connection.on('RO_ACCESS_REPORT', function(msg) {\n+            that.processTagReport(msg);\n+        });\n+\n+        // Handler para notificaciones de eventos del reader\n+        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {\n+            console.log('📢 READER_EVENT_NOTIFICATION recibido');\n+        });\n+    }\n+\n+    processTagReport(msg) {\n+        try {\n+            const tagReportData = msg.getTagReportData ? msg.getTagReportData() : [];\n+            const tags = Array.isArray(tagReportData) ? tagReportData : [tagReportData];\n+\n+            for (const tag of tags) {\n+                if (tag && tag.getEPCParameter) {\n+                    const epcParam = tag.getEPCParameter();\n+                    const epc = epcParam ? epcParam.getEPC() : null;\n+                    const antennaId = tag.getAntennaID ? tag.getAntennaID() : 'Unknown';\n+                    const rssi = tag.getPeakRSSI ? tag.getPeakRSSI() : null;\n+                    const timestamp = new Date().toISOString();\n+\n+                    if (epc) {\n+                        this.logTag(epc, antennaId, rssi, timestamp);\n+                    }\n+                }\n+            }\n+        } catch (error) {\n+            console.error('❌ Error procesando reporte de tags:', error.message);\n+        }\n+    }\n+\n+    logTag(epc, antenna, rssi, timestamp) {\n+        this.stats.totalTags++;\n+        this.stats.uniqueTags.add(epc);\n+        this.stats.lastTagTime = timestamp;\n+\n+        if (!this.stats.antennaStats[antenna]) {\n+            this.stats.antennaStats[antenna] = 0;\n+        }\n+        this.stats.antennaStats[antenna]++;\n+\n+        // Log a consola\n+        const logMessage = `📡 TAG: ${epc} | Antena: ${antenna} | RSSI: ${rssi} | ${timestamp}`;\n+        console.log(logMessage);\n+\n+        // Log a archivo\n+        const fileMessage = `${timestamp},${epc},${antenna},${rssi}\\n`;\n+        this.outputStream.write(fileMessage);\n+\n+        // Mostrar estadísticas cada 10 tags\n+        if (this.stats.totalTags % 10 === 0) {\n+            this.showStats();\n+        }\n+    }\n+\n+    showStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS:');\n+        console.log(`   Total tags: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo ejecución: ${runtime}s`);\n+        console.log(`   Tags por antena:`, this.stats.antennaStats);\n+        console.log('');\n+    }\n+\n+    async initializeReader() {\n+        try {\n+            console.log('🔧 Inicializando reader...');\n+\n+            // Esperar confirmación de conexión\n+            await this.waitForConnectionConfirmation();\n+\n+            // Limpiar ROSpecs existentes\n+            await this.cleanupROSpecs();\n+\n+            // Crear y configurar ROSpec\n+            await this.setupROSpec();\n+\n+            console.log('✅ Reader inicializado correctamente');\n+            return true;\n+\n+        } catch (error) {\n+            console.error('❌ Error inicializando reader:', error.message);\n+            return false;\n+        }\n+    }\n+\n+    async waitForConnectionConfirmation() {\n+        console.log('⏳ Esperando confirmación de conexión...');\n+\n+        return new Promise((resolve, reject) => {\n+            const timeout = setTimeout(() => {\n+                reject(new Error('Timeout esperando confirmación'));\n+            }, this.config.timeout);\n+\n+            const checkConnection = () => {\n+                if (this.isConnected) {\n+                    clearTimeout(timeout);\n+                    resolve();\n+                } else {\n+                    setTimeout(checkConnection, 100);\n+                }\n+            };\n+\n+            checkConnection();\n+        });\n+    }\n+\n+    async cleanupROSpecs() {\n+        try {\n+            console.log('🗑️ Limpiando ROSpecs existentes...');\n+\n+            // Obtener ROSpecs existentes\n+            const response = await Promise.race([\n+                this.connection.transact(new this.LLRPCore.GET_ROSPECS()),\n+                new Promise((_, reject) =>\n+                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)\n+                )\n+            ]);\n+\n+            // Procesar ROSpecs\n+            let rospecs = response.getROSpec ? response.getROSpec() : [];\n+            if (!Array.isArray(rospecs)) {\n+                rospecs = rospecs ? [rospecs] : [];\n+            }\n+\n+            // Eliminar cada ROSpec\n+            for (const rospec of rospecs) {\n+                const rospecId = rospec.getROSpecID ? rospec.getROSpecID() : null;\n+                if (rospecId) {\n+                    try {\n+                        await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                            data: { ROSpecID: rospecId }\n+                        }));\n+                        console.log(`   ✅ ROSpec ${rospecId} eliminado`);\n+                    } catch (error) {\n+                        console.log(`   ⚠️ Error eliminando ROSpec ${rospecId}:`, error.message);\n+                    }\n+                }\n+            }\n+\n+        } catch (error) {\n+            console.log('⚠️ Error en limpieza de ROSpecs:', error.message);\n+        }\n+    }\n+\n+    async setupROSpec() {\n+        try {\n+            console.log('📡 Configurando ROSpec...');\n+\n+            // Crear ROSpec\n+            await this.connection.transact(new this.LLRPCore.ADD_ROSPEC(this.rospecConfig));\n+            console.log('✅ ROSpec creado');\n+\n+            // Habilitar ROSpec\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+            await this.connection.transact(new this.LLRPCore.ENABLE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec habilitado');\n+\n+            // Iniciar ROSpec\n+            await this.connection.transact(new this.LLRPCore.START_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+            console.log('✅ ROSpec iniciado');\n+\n+            console.log('🎉 Reader listo para detectar tags');\n+\n+        } catch (error) {\n+            throw new Error(`Error configurando ROSpec: ${error.message}`);\n+        }\n+    }\n+\n+    async stopROSpec() {\n+        try {\n+            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n+\n+            console.log('🛑 Deteniendo ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.STOP_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('🗑️ Eliminando ROSpec...');\n+            await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n+                data: { ROSpecID: rospecId }\n+            }));\n+\n+            console.log('✅ ROSpec detenido y eliminado');\n+\n+        } catch (error) {\n+            console.log('⚠️ Error deteniendo ROSpec:', error.message);\n+        }\n+    }\n+\n+    scheduleReconnect() {\n+        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {\n+            console.error('❌ Máximo número de intentos de reconexión alcanzado');\n+            this.stop();\n+            return;\n+        }\n+\n+        this.reconnectAttempts++;\n+        console.log(`🔄 Reintentando conexión en ${this.config.reconnectInterval}ms (intento ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);\n+\n+        setTimeout(async () => {\n+            if (this.isRunning) {\n+                const connected = await this.connect();\n+                if (connected) {\n+                    await this.initializeReader();\n+                }\n+            }\n+        }, this.config.reconnectInterval);\n+    }\n+\n+    async start() {\n+        console.log('🚀 Iniciando lectura de tags...');\n+        this.isRunning = true;\n+        this.stats.startTime = Date.now();\n+\n+        // Conectar al reader\n+        const connected = await this.connect();\n+        if (!connected) {\n+            console.error('❌ No se pudo conectar al reader');\n+            return false;\n+        }\n+\n+        // Inicializar reader\n+        const initialized = await this.initializeReader();\n+        if (!initialized) {\n+            console.error('❌ No se pudo inicializar el reader');\n+            return false;\n+        }\n+\n+        console.log('✅ Lectura de tags iniciada');\n+        console.log('📝 Presiona Ctrl+C para detener');\n+\n+        return true;\n+    }\n+\n+    async stop() {\n+        console.log('🛑 Deteniendo lectura de tags...');\n+        this.isRunning = false;\n+\n+        if (this.connection && this.isConnected) {\n+            await this.stopROSpec();\n+\n+            try {\n+                await this.connection.transact(new this.LLRPCore.CLOSE_CONNECTION());\n+                this.connection.disconnect();\n+            } catch (error) {\n+                console.log('⚠️ Error cerrando conexión:', error.message);\n+            }\n+        }\n+\n+        if (this.outputStream) {\n+            const footer = `=== SESIÓN FINALIZADA: ${new Date().toISOString()} ===\\n`;\n+            this.outputStream.write(footer);\n+            this.outputStream.end();\n+        }\n+\n+        this.showFinalStats();\n+        console.log('✅ Lectura detenida');\n+    }\n+\n+    showFinalStats() {\n+        const runtime = this.stats.startTime ?\n+            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n+\n+        console.log('\\n📊 ESTADÍSTICAS FINALES:');\n+        console.log(`   Total tags detectados: ${this.stats.totalTags}`);\n+        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n+        console.log(`   Tiempo total: ${runtime}s`);\n+        console.log(`   Promedio: ${runtime > 0 ? (this.stats.totalTags / runtime).toFixed(2) : 0} tags/s`);\n+        console.log(`   Distribución por antena:`, this.stats.antennaStats);\n+        console.log(`   Último tag: ${this.stats.lastTagTime || 'N/A'}`);\n+        console.log('');\n+    }\n+}\n+\n+// Función para parsear argumentos de línea de comandos\n+function parseArgs() {\n+    const args = process.argv.slice(2);\n+    const options = {};\n+\n+    for (let i = 0; i < args.length; i++) {\n+        const arg = args[i];\n+\n+        switch (arg) {\n+            case '--help':\n+            case '-h':\n+                showHelp();\n+                process.exit(0);\n+                break;\n+            case '--config':\n+                options.config = args[++i];\n+                break;\n+            case '--rospec':\n+                options.rospec = args[++i];\n+                break;\n+            case '--output':\n+                options.output = args[++i];\n+                break;\n+            case '--host':\n+                options.host = args[++i];\n+                break;\n+            case '--port':\n+                options.port = args[++i];\n+                break;\n+            default:\n+                if (arg.startsWith('--')) {\n+                    console.error(`❌ Opción desconocida: ${arg}`);\n+                    showHelp();\n+                    process.exit(1);\n+                }\n+        }\n+    }\n+\n+    return options;\n+}\n+\n+function showHelp() {\n+    console.log(`\n+🦓 Zebra FX9600 RFID Reader Tester\n+\n+Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n+\n+USO:\n+    node zebra-rfid-tester.js [opciones]\n+\n+OPCIONES:\n+    --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n+    --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n+    --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n+    --host <ip>          IP del reader (override config)\n+    --port <puerto>      Puerto del reader (override config)\n+    --help, -h           Mostrar esta ayuda\n+\n+EJEMPLOS:\n+    # Usar configuración por defecto\n+    node zebra-rfid-tester.js\n+\n+    # Especificar IP y puerto\n+    node zebra-rfid-tester.js --host ************* --port 5084\n+\n+    # Usar archivos de configuración personalizados\n+    node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json\n+\n+    # Guardar logs en archivo específico\n+    node zebra-rfid-tester.js --output mis-tags.log\n+\n+ARCHIVOS GENERADOS:\n+    zebra-config.json     Configuración del reader (IP, puerto, timeouts)\n+    zebra-rospec.json     Configuración ROSpec (antenas, parámetros RFID)\n+    zebra-tags.log        Log de tags detectados (CSV format)\n+\n+FORMATO DEL LOG:\n+    timestamp,epc,antenna,rssi\n+    2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45\n+\n+MONITOREO EN TIEMPO REAL:\n+    # En otra terminal, para ver tags en tiempo real:\n+    tail -f zebra-tags.log\n+\n+    # Para ver solo los EPCs:\n+    tail -f zebra-tags.log | cut -d',' -f2\n+\n+    # Para contar tags únicos:\n+    tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l\n+`);\n+}\n+\n+// Función principal\n+async function main() {\n+    console.log('🦓 Zebra FX9600 RFID Reader Tester v1.0');\n+    console.log('=====================================\\n');\n+\n+    // Parsear argumentos\n+    const options = parseArgs();\n+\n+    // Crear instancia del tester\n+    const tester = new ZebraRFIDTester(options);\n+\n+    // Los manejadores de señales ya están configurados en el constructor de la clase\n+\n+    try {\n+        // Inicializar\n+        const initialized = await tester.init();\n+        if (!initialized) {\n+            console.error('❌ Error en inicialización');\n+            process.exit(1);\n+        }\n+\n+        // Iniciar lectura\n+        const started = await tester.start();\n+        if (!started) {\n+            console.error('❌ Error iniciando lectura');\n+            process.exit(1);\n+        }\n+\n+        // Mantener el proceso vivo\n+        console.log('🔄 Leyendo tags... (Ctrl+C para detener)');\n+\n+        // Mostrar estadísticas cada 30 segundos\n+        setInterval(() => {\n+            if (tester.isRunning) {\n+                tester.showStats();\n+            }\n+        }, 30000);\n+\n+    } catch (error) {\n+        console.error('❌ Error fatal:', error.message);\n+        await tester.stop();\n+        process.exit(1);\n+    }\n+}\n+\n+// Ejecutar solo si es el archivo principal\n+if (require.main === module) {\n+    main().catch(error => {\n+        console.error('❌ Error en main:', error);\n+        process.exit(1);\n+    });\n+}\n+\n+module.exports = ZebraRFIDTester;\n"}], "date": 1758058241457, "name": "Commit-0", "content": "#!/usr/bin/env node\n\n/**\n * Zebra FX9600 RFID Reader Tester\n * Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n *\n * Uso:\n *   node zebra-rfid-tester.js [opciones]\n *\n * Opciones:\n *   --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n *   --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n *   --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n *   --host <ip>          IP del reader (override config)\n *   --port <puerto>      Puerto del reader (override config)\n *   --help               Mostrar ayuda\n */\n\nconst fs = require('fs');\nconst path = require('path');\n\n// Configuración por defecto\nconst DEFAULT_CONFIG = {\n    host: '*************',\n    port: 5084,\n    timeout: 10000,\n    reconnectInterval: 5000,\n    maxReconnectAttempts: 5\n};\n\nconst DEFAULT_FILES = {\n    config: 'zebra-config.json',\n    rospec: 'zebra-rospec.json',\n    output: 'zebra-tags.log'\n};\n\nclass ZebraRFIDTester {\n    constructor(options = {}) {\n        this.options = { ...DEFAULT_FILES, ...options };\n        this.config = null;\n        this.rospecConfig = null;\n        this.connection = null;\n        this.isConnected = false;\n        this.isRunning = false;\n        this.reconnectAttempts = 0;\n        this.tagCount = 0;\n        this.startTime = null;\n        this.outputStream = null;\n        this.gracefulShutdown = false;\n\n        // Estadísticas\n        this.stats = {\n            totalTags: 0,\n            uniqueTags: new Set(),\n            antennaStats: {},\n            lastTagTime: null,\n            startTime: null\n        };\n\n        // Configurar manejo de señales mejorado\n        this.setupSignalHandlers();\n    }\n\n    setupSignalHandlers() {\n        // Manejar Ctrl+C de forma más robusta\n        process.on('SIGINT', () => {\n            if (!this.gracefulShutdown) {\n                console.log('\\n🛑 Ctrl+C detectado - Iniciando cierre seguro...');\n                this.gracefulShutdown = true;\n                this.stop().then(() => {\n                    console.log('✅ Cierre completado');\n                    process.exit(0);\n                }).catch(() => {\n                    console.log('⚠️ Forzando cierre...');\n                    process.exit(1);\n                });\n\n                // Timeout de seguridad\n                setTimeout(() => {\n                    console.log('⚠️ Timeout de cierre - Forzando salida');\n                    process.exit(1);\n                }, 5000);\n            }\n        });\n\n        process.on('SIGTERM', () => {\n            console.log('\\n🛑 SIGTERM recibido...');\n            this.stop().then(() => process.exit(0));\n        });\n\n        // Manejar errores no capturados\n        process.on('unhandledRejection', (reason) => {\n            console.error('❌ Promesa rechazada:', reason);\n            if (!this.gracefulShutdown) {\n                this.stop().then(() => process.exit(1));\n            }\n        });\n\n        process.on('uncaughtException', (error) => {\n            console.error('❌ Excepción no capturada:', error);\n            if (!this.gracefulShutdown) {\n                this.stop().then(() => process.exit(1));\n            }\n        });\n    }\n\n    async init() {\n        console.log('🚀 Zebra FX9600 RFID Tester - Iniciando...');\n\n        try {\n            // Cargar configuración\n            await this.loadConfig();\n\n            // Cargar ROSpec\n            await this.loadROSpec();\n\n            // Configurar archivo de salida\n            this.setupOutput();\n\n            // Cargar librería LLRP\n            this.loadLLRPLibrary();\n\n            console.log('✅ Inicialización completada');\n            return true;\n\n        } catch (error) {\n            console.error('❌ Error en inicialización:', error.message);\n            return false;\n        }\n    }\n\n    loadLLRPLibrary() {\n        try {\n            console.log('📦 Cargando librería llrpjs...');\n            const llrpjs = require('llrpjs');\n            this.LLRPClient = llrpjs.LLRPClient;\n            this.LLRPCore = llrpjs.LLRPCore;\n            console.log('✅ Librería llrpjs cargada correctamente');\n        } catch (error) {\n            throw new Error(`No se pudo cargar llrpjs: ${error.message}`);\n        }\n    }\n\n    async loadConfig() {\n        const configFile = this.options.config;\n\n        if (!fs.existsSync(configFile)) {\n            console.log(`📝 Creando archivo de configuración: ${configFile}`);\n            fs.writeFileSync(configFile, JSON.stringify(DEFAULT_CONFIG, null, 2));\n        }\n\n        const configData = fs.readFileSync(configFile, 'utf8');\n        this.config = JSON.parse(configData);\n\n        // Override con opciones de línea de comandos\n        if (this.options.host) this.config.host = this.options.host;\n        if (this.options.port) this.config.port = parseInt(this.options.port);\n\n        console.log('📋 Configuración cargada:');\n        console.log(`   Host: ${this.config.host}`);\n        console.log(`   Puerto: ${this.config.port}`);\n        console.log(`   Timeout: ${this.config.timeout}ms`);\n    }\n\n    async loadROSpec() {\n        const rospecFile = this.options.rospec;\n\n        if (!fs.existsSync(rospecFile)) {\n            console.log(`📝 Creando archivo ROSpec por defecto: ${rospecFile}`);\n            const defaultROSpec = this.createDefaultROSpec();\n            fs.writeFileSync(rospecFile, JSON.stringify(defaultROSpec, null, 2));\n        }\n\n        const rospecData = fs.readFileSync(rospecFile, 'utf8');\n        this.rospecConfig = JSON.parse(rospecData);\n\n        console.log('📡 ROSpec cargado:');\n        console.log(`   ROSpec ID: ${this.rospecConfig.data.ROSpec.ROSpecID}`);\n        console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);\n    }\n\n    createDefaultROSpec() {\n        return {\n            \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n            \"id\": 103,\n            \"type\": \"ADD_ROSPEC\",\n            \"data\": {\n                \"ROSpec\": {\n                    \"ROSpecID\": 1,\n                    \"Priority\": 0,\n                    \"CurrentState\": \"Disabled\",\n                    \"ROBoundarySpec\": {\n                        \"ROSpecStartTrigger\": {\n                            \"ROSpecStartTriggerType\": \"Immediate\"\n                        },\n                        \"ROSpecStopTrigger\": {\n                            \"ROSpecStopTriggerType\": \"Null\"\n                        }\n                    },\n                    \"AISpec\": {\n                        \"AntennaIDs\": [1, 2, 3, 4],\n                        \"AISpecStopTrigger\": {\n                            \"AISpecStopTriggerType\": \"Null\"\n                        },\n                        \"InventoryParameterSpec\": {\n                            \"InventoryParameterSpecID\": 1,\n                            \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n                        }\n                    },\n                    \"ROReportSpec\": {\n                        \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n                        \"N\": 1,\n                        \"TagReportContentSelector\": {\n                            \"EnableROSpecID\": false,\n                            \"EnableSpecIndex\": false,\n                            \"EnableInventoryParameterSpecID\": false,\n                            \"EnableAntennaID\": true,\n                            \"EnableChannelIndex\": false,\n                            \"EnablePeakRSSI\": true,\n                            \"EnableFirstSeenTimestamp\": true,\n                            \"EnableLastSeenTimestamp\": true,\n                            \"EnableTagSeenCount\": true,\n                            \"EnableAccessSpecID\": false\n                        }\n                    }\n                }\n            }\n        };\n    }\n\n    setupOutput() {\n        const outputFile = this.options.output;\n        console.log(`📄 Archivo de salida: ${outputFile}`);\n\n        // Crear stream de escritura\n        this.outputStream = fs.createWriteStream(outputFile, { flags: 'a' });\n\n        // Escribir header\n        const header = `\\n=== SESIÓN INICIADA: ${new Date().toISOString()} ===\\n`;\n        this.outputStream.write(header);\n        console.log(`📝 Logs se guardarán en: ${outputFile}`);\n    }\n\n    async connect() {\n        try {\n            console.log('🔗 Conectando al reader...');\n\n            // Crear cliente LLRP\n            this.connection = new this.LLRPClient({\n                host: this.config.host,\n                port: this.config.port\n            });\n\n            // Configurar event handlers\n            this.setupEventHandlers();\n\n            // Conectar con timeout\n            await Promise.race([\n                this.connection.connect(),\n                new Promise((_, reject) =>\n                    setTimeout(() => reject(new Error('Timeout de conexión')), this.config.timeout)\n                )\n            ]);\n\n            this.isConnected = true;\n            this.reconnectAttempts = 0;\n            console.log('✅ Conectado al reader Zebra FX9600');\n\n            return true;\n\n        } catch (error) {\n            console.error('❌ Error de conexión:', error.message);\n            this.isConnected = false;\n            return false;\n        }\n    }\n\n    setupEventHandlers() {\n        const that = this;\n\n        this.connection.on('error', function(error) {\n            console.error('❌ ERROR DE CONEXIÓN:', error.message);\n            that.isConnected = false;\n            that.scheduleReconnect();\n        });\n\n        this.connection.on('connect', function() {\n            console.log('🔗 Evento CONNECT recibido');\n        });\n\n        this.connection.on('disconnect', function() {\n            console.log('🔌 Evento DISCONNECT recibido');\n            that.isConnected = false;\n            if (that.isRunning) {\n                that.scheduleReconnect();\n            }\n        });\n\n        // Handler para reportes de tags RFID\n        this.connection.on('RO_ACCESS_REPORT', function(msg) {\n            that.processTagReport(msg);\n        });\n\n        // Handler para notificaciones de eventos del reader\n        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {\n            console.log('📢 READER_EVENT_NOTIFICATION recibido');\n        });\n    }\n\n    processTagReport(msg) {\n        try {\n            const tagReportData = msg.getTagReportData ? msg.getTagReportData() : [];\n            const tags = Array.isArray(tagReportData) ? tagReportData : [tagReportData];\n\n            for (const tag of tags) {\n                if (tag && tag.getEPCParameter) {\n                    const epcParam = tag.getEPCParameter();\n                    const epc = epcParam ? epcParam.getEPC() : null;\n                    const antennaId = tag.getAntennaID ? tag.getAntennaID() : 'Unknown';\n                    const rssi = tag.getPeakRSSI ? tag.getPeakRSSI() : null;\n                    const timestamp = new Date().toISOString();\n\n                    if (epc) {\n                        this.logTag(epc, antennaId, rssi, timestamp);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('❌ Error procesando reporte de tags:', error.message);\n        }\n    }\n\n    logTag(epc, antenna, rssi, timestamp) {\n        this.stats.totalTags++;\n        this.stats.uniqueTags.add(epc);\n        this.stats.lastTagTime = timestamp;\n\n        if (!this.stats.antennaStats[antenna]) {\n            this.stats.antennaStats[antenna] = 0;\n        }\n        this.stats.antennaStats[antenna]++;\n\n        // Log a consola\n        const logMessage = `📡 TAG: ${epc} | Antena: ${antenna} | RSSI: ${rssi} | ${timestamp}`;\n        console.log(logMessage);\n\n        // Log a archivo\n        const fileMessage = `${timestamp},${epc},${antenna},${rssi}\\n`;\n        this.outputStream.write(fileMessage);\n\n        // Mostrar estadísticas cada 10 tags\n        if (this.stats.totalTags % 10 === 0) {\n            this.showStats();\n        }\n    }\n\n    showStats() {\n        const runtime = this.stats.startTime ?\n            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n\n        console.log('\\n📊 ESTADÍSTICAS:');\n        console.log(`   Total tags: ${this.stats.totalTags}`);\n        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n        console.log(`   Tiempo ejecución: ${runtime}s`);\n        console.log(`   Tags por antena:`, this.stats.antennaStats);\n        console.log('');\n    }\n\n    async initializeReader() {\n        try {\n            console.log('🔧 Inicializando reader...');\n\n            // Esperar confirmación de conexión\n            await this.waitForConnectionConfirmation();\n\n            // Limpiar ROSpecs existentes\n            await this.cleanupROSpecs();\n\n            // Crear y configurar ROSpec\n            await this.setupROSpec();\n\n            console.log('✅ Reader inicializado correctamente');\n            return true;\n\n        } catch (error) {\n            console.error('❌ Error inicializando reader:', error.message);\n            return false;\n        }\n    }\n\n    async waitForConnectionConfirmation() {\n        console.log('⏳ Esperando confirmación de conexión...');\n\n        return new Promise((resolve, reject) => {\n            const timeout = setTimeout(() => {\n                reject(new Error('Timeout esperando confirmación'));\n            }, this.config.timeout);\n\n            const checkConnection = () => {\n                if (this.isConnected) {\n                    clearTimeout(timeout);\n                    resolve();\n                } else {\n                    setTimeout(checkConnection, 100);\n                }\n            };\n\n            checkConnection();\n        });\n    }\n\n    async cleanupROSpecs() {\n        try {\n            console.log('🗑️ Limpiando ROSpecs existentes...');\n\n            // Obtener ROSpecs existentes\n            const response = await Promise.race([\n                this.connection.transact(new this.LLRPCore.GET_ROSPECS()),\n                new Promise((_, reject) =>\n                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)\n                )\n            ]);\n\n            // Procesar ROSpecs\n            let rospecs = response.getROSpec ? response.getROSpec() : [];\n            if (!Array.isArray(rospecs)) {\n                rospecs = rospecs ? [rospecs] : [];\n            }\n\n            // Eliminar cada ROSpec\n            for (const rospec of rospecs) {\n                const rospecId = rospec.getROSpecID ? rospec.getROSpecID() : null;\n                if (rospecId) {\n                    try {\n                        await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n                            data: { ROSpecID: rospecId }\n                        }));\n                        console.log(`   ✅ ROSpec ${rospecId} eliminado`);\n                    } catch (error) {\n                        console.log(`   ⚠️ Error eliminando ROSpec ${rospecId}:`, error.message);\n                    }\n                }\n            }\n\n        } catch (error) {\n            console.log('⚠️ Error en limpieza de ROSpecs:', error.message);\n        }\n    }\n\n    async setupROSpec() {\n        try {\n            console.log('📡 Configurando ROSpec...');\n\n            // Crear ROSpec\n            await this.connection.transact(new this.LLRPCore.ADD_ROSPEC(this.rospecConfig));\n            console.log('✅ ROSpec creado');\n\n            // Habilitar ROSpec\n            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n            await this.connection.transact(new this.LLRPCore.ENABLE_ROSPEC({\n                data: { ROSpecID: rospecId }\n            }));\n            console.log('✅ ROSpec habilitado');\n\n            // Iniciar ROSpec\n            await this.connection.transact(new this.LLRPCore.START_ROSPEC({\n                data: { ROSpecID: rospecId }\n            }));\n            console.log('✅ ROSpec iniciado');\n\n            console.log('🎉 Reader listo para detectar tags');\n\n        } catch (error) {\n            throw new Error(`Error configurando ROSpec: ${error.message}`);\n        }\n    }\n\n    async stopROSpec() {\n        try {\n            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;\n\n            console.log('🛑 Deteniendo ROSpec...');\n            await this.connection.transact(new this.LLRPCore.STOP_ROSPEC({\n                data: { ROSpecID: rospecId }\n            }));\n\n            console.log('🗑️ Eliminando ROSpec...');\n            await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({\n                data: { ROSpecID: rospecId }\n            }));\n\n            console.log('✅ ROSpec detenido y eliminado');\n\n        } catch (error) {\n            console.log('⚠️ Error deteniendo ROSpec:', error.message);\n        }\n    }\n\n    scheduleReconnect() {\n        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {\n            console.error('❌ Máximo número de intentos de reconexión alcanzado');\n            this.stop();\n            return;\n        }\n\n        this.reconnectAttempts++;\n        console.log(`🔄 Reintentando conexión en ${this.config.reconnectInterval}ms (intento ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);\n\n        setTimeout(async () => {\n            if (this.isRunning) {\n                const connected = await this.connect();\n                if (connected) {\n                    await this.initializeReader();\n                }\n            }\n        }, this.config.reconnectInterval);\n    }\n\n    async start() {\n        console.log('🚀 Iniciando lectura de tags...');\n        this.isRunning = true;\n        this.stats.startTime = Date.now();\n\n        // Conectar al reader\n        const connected = await this.connect();\n        if (!connected) {\n            console.error('❌ No se pudo conectar al reader');\n            return false;\n        }\n\n        // Inicializar reader\n        const initialized = await this.initializeReader();\n        if (!initialized) {\n            console.error('❌ No se pudo inicializar el reader');\n            return false;\n        }\n\n        console.log('✅ Lectura de tags iniciada');\n        console.log('📝 Presiona Ctrl+C para detener');\n\n        return true;\n    }\n\n    async stop() {\n        console.log('🛑 Deteniendo lectura de tags...');\n        this.isRunning = false;\n\n        if (this.connection && this.isConnected) {\n            await this.stopROSpec();\n\n            try {\n                await this.connection.transact(new this.LLRPCore.CLOSE_CONNECTION());\n                this.connection.disconnect();\n            } catch (error) {\n                console.log('⚠️ Error cerrando conexión:', error.message);\n            }\n        }\n\n        if (this.outputStream) {\n            const footer = `=== SESIÓN FINALIZADA: ${new Date().toISOString()} ===\\n`;\n            this.outputStream.write(footer);\n            this.outputStream.end();\n        }\n\n        this.showFinalStats();\n        console.log('✅ Lectura detenida');\n    }\n\n    showFinalStats() {\n        const runtime = this.stats.startTime ?\n            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;\n\n        console.log('\\n📊 ESTADÍSTICAS FINALES:');\n        console.log(`   Total tags detectados: ${this.stats.totalTags}`);\n        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);\n        console.log(`   Tiempo total: ${runtime}s`);\n        console.log(`   Promedio: ${runtime > 0 ? (this.stats.totalTags / runtime).toFixed(2) : 0} tags/s`);\n        console.log(`   Distribución por antena:`, this.stats.antennaStats);\n        console.log(`   Último tag: ${this.stats.lastTagTime || 'N/A'}`);\n        console.log('');\n    }\n}\n\n// Función para parsear argumentos de línea de comandos\nfunction parseArgs() {\n    const args = process.argv.slice(2);\n    const options = {};\n\n    for (let i = 0; i < args.length; i++) {\n        const arg = args[i];\n\n        switch (arg) {\n            case '--help':\n            case '-h':\n                showHelp();\n                process.exit(0);\n                break;\n            case '--config':\n                options.config = args[++i];\n                break;\n            case '--rospec':\n                options.rospec = args[++i];\n                break;\n            case '--output':\n                options.output = args[++i];\n                break;\n            case '--host':\n                options.host = args[++i];\n                break;\n            case '--port':\n                options.port = args[++i];\n                break;\n            default:\n                if (arg.startsWith('--')) {\n                    console.error(`❌ Opción desconocida: ${arg}`);\n                    showHelp();\n                    process.exit(1);\n                }\n        }\n    }\n\n    return options;\n}\n\nfunction showHelp() {\n    console.log(`\n🦓 Zebra FX9600 RFID Reader Tester\n\nHerramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP\n\nUSO:\n    node zebra-rfid-tester.js [opciones]\n\nOPCIONES:\n    --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)\n    --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)\n    --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)\n    --host <ip>          IP del reader (override config)\n    --port <puerto>      Puerto del reader (override config)\n    --help, -h           Mostrar esta ayuda\n\nEJEMPLOS:\n    # Usar configuración por defecto\n    node zebra-rfid-tester.js\n\n    # Especificar IP y puerto\n    node zebra-rfid-tester.js --host ************* --port 5084\n\n    # Usar archivos de configuración personalizados\n    node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json\n\n    # Guardar logs en archivo específico\n    node zebra-rfid-tester.js --output mis-tags.log\n\nARCHIVOS GENERADOS:\n    zebra-config.json     Configuración del reader (IP, puerto, timeouts)\n    zebra-rospec.json     Configuración ROSpec (antenas, parámetros RFID)\n    zebra-tags.log        Log de tags detectados (CSV format)\n\nFORMATO DEL LOG:\n    timestamp,epc,antenna,rssi\n    2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45\n\nMONITOREO EN TIEMPO REAL:\n    # En otra terminal, para ver tags en tiempo real:\n    tail -f zebra-tags.log\n\n    # Para ver solo los EPCs:\n    tail -f zebra-tags.log | cut -d',' -f2\n\n    # Para contar tags únicos:\n    tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l\n`);\n}\n\n// Función principal\nasync function main() {\n    console.log('🦓 Zebra FX9600 RFID Reader Tester v1.0');\n    console.log('=====================================\\n');\n\n    // Parsear argumentos\n    const options = parseArgs();\n\n    // Crear instancia del tester\n    const tester = new ZebraRFIDTester(options);\n\n    // Manejar señales de interrupción\n    process.on('SIGINT', async () => {\n        console.log('\\n🛑 Señal de interrupción recibida...');\n        await tester.stop();\n        process.exit(0);\n    });\n\n    process.on('SIGTERM', async () => {\n        console.log('\\n🛑 Señal de terminación recibida...');\n        await tester.stop();\n        process.exit(0);\n    });\n\n    // Manejar errores no capturados\n    process.on('unhandledRejection', (reason, promise) => {\n        console.error('❌ Promesa rechazada no manejada:', reason);\n        tester.stop().then(() => process.exit(1));\n    });\n\n    process.on('uncaughtException', (error) => {\n        console.error('❌ Excepción no capturada:', error);\n        tester.stop().then(() => process.exit(1));\n    });\n\n    try {\n        // Inicializar\n        const initialized = await tester.init();\n        if (!initialized) {\n            console.error('❌ Error en inicialización');\n            process.exit(1);\n        }\n\n        // Iniciar lectura\n        const started = await tester.start();\n        if (!started) {\n            console.error('❌ Error iniciando lectura');\n            process.exit(1);\n        }\n\n        // Mantener el proceso vivo\n        console.log('🔄 Leyendo tags... (Ctrl+C para detener)');\n\n        // Mostrar estadísticas cada 30 segundos\n        setInterval(() => {\n            if (tester.isRunning) {\n                tester.showStats();\n            }\n        }, 30000);\n\n    } catch (error) {\n        console.error('❌ Error fatal:', error.message);\n        await tester.stop();\n        process.exit(1);\n    }\n}\n\n// Ejecutar solo si es el archivo principal\nif (require.main === module) {\n    main().catch(error => {\n        console.error('❌ Error en main:', error);\n        process.exit(1);\n    });\n}\n\nmodule.exports = ZebraRFIDTester;\n"}]}