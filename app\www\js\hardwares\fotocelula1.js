
var fotocelula3 = {

    'config': {
        'enter': true,
        'baudios': 0,
        'puerto': false
    },

    'ultimo': 0,

    conectar: function () {
        var that = this;
        document.addEventListener("keydown", that.recibir, false);
    },

    desconectar: function () {
        var that = this;
        document.removeEventListener("keydown", that.recibir, false);
    },

    recibir: function (e) {
        var charCode = (e.which) ? e.which : e.keyCode;
        if (charCode == 32) {
            e.preventDefault();
            let date = new Date();
            if (typeof this.ultimo == 'undefined')
                this.ultimo = 0;
            if (date.getTime() - this.ultimo > 524)
                app.crono();
            this.ultimo = date.getTime();
        }
    },

    validar: function (data) {
        // Tiene que ser un espacio
        if (data[0] == 32)
            return true;
        else
            return false;
   }

}
