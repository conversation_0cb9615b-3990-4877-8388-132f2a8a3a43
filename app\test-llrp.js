// Test simple para verificar que llrpjs funciona
console.log('🧪 Probando llrpjs...');

try {
    const llrpjs = require('llrpjs');
    console.log('✅ llrpjs cargado correctamente');
    console.log('📦 LLRPClient disponible:', typeof llrpjs.LLRPClient);
    console.log('📦 LLRPCore disponible:', typeof llrpjs.LLRPCore);
    
    // Crear una instancia de prueba (sin conectar)
    const client = new llrpjs.LLRPClient({
        host: '*************',
        port: 5084
    });
    console.log('✅ Cliente LLRP creado correctamente');
    
} catch (error) {
    console.error('❌ Error:', error.message);
}

console.log('🏁 Test completado');
