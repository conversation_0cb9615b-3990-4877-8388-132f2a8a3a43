{"sourceFile": "app/www/index.html", "activeCommit": 0, "commits": [{"activePatchIndex": 44, "patches": [{"date": 1756849088673, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1756850852134, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -71,13 +71,21 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            rapida: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.inicializacionRapida();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             ayuda: function() {\r\n                 console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n                 console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n+                console.log('   zebraTest.rapida()     - Inicialización rápida (omite DELETE/RESET)');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n         };\r\n \r\n"}, {"date": 1756851235330, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -78,8 +78,15 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            reconectar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.reconectar();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             ayuda: function() {\r\n                 console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n"}, {"date": 1756851248371, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -90,8 +90,9 @@\n                 console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n                 console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n+                console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n                 console.log('   zebraTest.rapida()     - Inicialización rápida (omite DELETE/RESET)');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n         };\r\n"}, {"date": 1756851714624, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -131,12 +131,8 @@\n \r\n             <div id=\"penas\" style=\"text-align: center;\"></div>\r\n \r\n             <div id=\"crono-keys\">\r\n-                <h1 style=\"color: white;\">CONTROL DE CHIPS</h1>\r\n-                <p style=\"color: white;\">Acerca el chip al lector y comprueba tus datos</p>\r\n-<!--\r\n-\r\n                 <div class=\"ui-grid-c ui-corner-all\">\r\n                     <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('buscador')\" data-icon=\"search\" id=\"buscador-btn\"></a></div>\r\n                     <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('1')\" alt=\"1\">1</a></div>\r\n                     <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('2')\" alt=\"2\">2</a></div>\r\n@@ -171,10 +167,8 @@\n                     <div id=\"buscando-gif\" style=\"display: none;\"><img src=\"images/ajax-loader-2.gif\"></div>\r\n                     <br>\r\n                 </div>\r\n \r\n-            -->\r\n-\r\n             </div>\r\n \r\n             <div id=\"totales\" class=\"ui-grid-a ui-corner-all\"></div>\r\n             <div id=\"consola\"></div>\r\n"}, {"date": 1756851764472, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -252,11 +252,13 @@\n         {{# extra }}\r\n         - <span class=\"extra\">{{ extra }}</span>\r\n         {{/ extra }}\r\n         {{^ extra }}\r\n+        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n         {{/ extra }}\r\n         - <span class=\"nombre\">{{ nombre }}</span><br>\r\n         <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"hora\">{{ hora }}</span>\r\n         - <span class=\"resultado\">{{ resultado }}</span>\r\n         <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n         <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n             <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n"}, {"date": 1756852567430, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,8 +67,13 @@\n                     console.log('   🧪 Modo test:', reader_zebra_fx9600.testMode);\r\n                     console.log('   ⏸️ Paso a paso:', reader_zebra_fx9600.stepByStep);\r\n                     console.log('   🔗 Conectado:', reader_zebra_fx9600.isConnected);\r\n                     console.log('   📡 Antena actual:', reader_zebra_fx9600.antena);\r\n+                    console.log('   📊 Modo RFID:', window.rfid_crono);\r\n+                    console.log('   📡 Antenas habilitadas:');\r\n+                    for (let i = 1; i <= 8; i++) {\r\n+                        console.log('      Antena ' + i + ':', window['antena_' + i] ? '✅' : '❌');\r\n+                    }\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n"}, {"date": 1756852904972, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -90,15 +90,25 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            debug: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🐛 ACTIVANDO LOGS DE DEBUG...');\r\n+                    reader_zebra_fx9600.debugMode = true;\r\n+                    console.log('✅ Logs de debug activados - ahora se mostrarán todos los logs');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             ayuda: function() {\r\n                 console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n                 console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n                 console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n                 console.log('   zebraTest.rapida()     - Inicialización rápida (omite DELETE/RESET)');\r\n+                console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n         };\r\n \r\n"}, {"date": 1756912677710, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -99,8 +99,37 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            watchdog: function() {\r\n+                console.log('🐕 INICIANDO WATCHDOG para detectar bloqueos...');\r\n+                let counter = 0;\r\n+                let lastTime = Date.now();\r\n+\r\n+                const watchdogInterval = setInterval(() => {\r\n+                    const now = Date.now();\r\n+                    const elapsed = now - lastTime;\r\n+\r\n+                    if (elapsed > 2000) { // Si pasan más de 2 segundos\r\n+                        console.error('🚨 BLOQUEO DETECTADO! Hilo principal bloqueado por', elapsed, 'ms');\r\n+                        console.error('🚨 Último contador:', counter);\r\n+                        console.error('🚨 Posible causa: operación síncrona o await sin timeout');\r\n+                    }\r\n+\r\n+                    lastTime = now;\r\n+                    counter++;\r\n+\r\n+                    if (counter % 10 === 0) {\r\n+                        console.log('🐕 Watchdog activo - contador:', counter);\r\n+                    }\r\n+                }, 1000);\r\n+\r\n+                // Detener después de 2 minutos\r\n+                setTimeout(() => {\r\n+                    clearInterval(watchdogInterval);\r\n+                    console.log('🐕 Watchdog detenido');\r\n+                }, 120000);\r\n+            },\r\n             ayuda: function() {\r\n                 console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n"}, {"date": 1756912693362, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -136,8 +136,9 @@\n                 console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n                 console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n                 console.log('   zebraTest.rapida()     - Inicialización rápida (omite DELETE/RESET)');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n+                console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n         };\r\n \r\n"}, {"date": 1756922682445, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -83,8 +83,21 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            minima: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🚀 INICIALIZACIÓN MÍNIMA - Solo ROSpec');\r\n+                    reader_zebra_fx9600.configurarROSpec().then(() => {\r\n+                        console.log('✅ Inicialización mínima completada');\r\n+                        reader_zebra_fx9600.finalizarInicializacion();\r\n+                    }).catch((error) => {\r\n+                        console.error('❌ Error en inicialización mínima:', error);\r\n+                    });\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756922700661, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -147,9 +147,10 @@\n                 console.log('   zebraTest.activar()    - Activa el modo test');\r\n                 console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n                 console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n                 console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n-                console.log('   zebraTest.rapida()     - Inicialización rápida (omite DELETE/RESET)');\r\n+                console.log('   zebraTest.rapida()     - Inicialización rápida (BLOQUEADA)');\r\n+                console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n"}, {"date": 1756922923821, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -86,13 +86,26 @@\n             },\r\n             minima: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     console.log('🚀 INICIALIZACIÓN MÍNIMA - Solo ROSpec');\r\n-                    reader_zebra_fx9600.configurarROSpec().then(() => {\r\n+\r\n+                    // Verificar conexión\r\n+                    if (!reader_zebra_fx9600.connection || !reader_zebra_fx9600.isConnected) {\r\n+                        console.log('❌ No hay conexión activa. Ejecuta zebraTest.reconectar() primero');\r\n+                        return;\r\n+                    }\r\n+\r\n+                    // Usar ROSpec ID diferente para evitar conflictos\r\n+                    let originalId = reader_zebra_fx9600.rospecId;\r\n+                    reader_zebra_fx9600.rospecId = Math.floor(Math.random() * 1000) + 100;\r\n+                    console.log('🔧 Usando ROSpec ID:', reader_zebra_fx9600.rospecId);\r\n+\r\n+                    reader_zebra_fx9600.configurarROSpecMinimo().then(() => {\r\n                         console.log('✅ Inicialización mínima completada');\r\n                         reader_zebra_fx9600.finalizarInicializacion();\r\n                     }).catch((error) => {\r\n                         console.error('❌ Error en inicialización mínima:', error);\r\n+                        reader_zebra_fx9600.rospecId = originalId; // Restaurar ID original\r\n                     });\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n"}, {"date": 1756923277285, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -109,8 +109,18 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            habilitar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('✅ HABILITANDO procesamiento de tags');\r\n+                    reader_zebra_fx9600.rospecActivo = true;\r\n+                    reader_zebra_fx9600.finalizarInicializacion();\r\n+                    console.log('🎉 Reader listo para procesar tags');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756923297567, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,652 @@\n+<!DOCTYPE html>\r\n+<html>\r\n+<head>\r\n+    <title>Cronometraje Instantaneo</title>\r\n+    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\r\n+    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\" >\r\n+    <link rel=\"stylesheet\" href=\"css/jquery.mobile-1.4.5.min.css\" />\r\n+    <link rel=\"stylesheet\" href=\"css/jquery.mobile-crono.css\" />\r\n+    <link rel=\"stylesheet\" href=\"css/app.css\" />\r\n+    <link rel=\"shortcut icon\" href=\"favicon.ico\">\r\n+\r\n+\r\n+    <!-- Insert this line above script imports  -->\r\n+    <script>if (typeof module === 'object') {window.module = module; module = undefined;}</script>\r\n+\r\n+    <!-- Scripts -->\r\n+    <script type=\"text/javascript\" src=\"js/jquery-2.1.1.min.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/jquery.mobile-1.4.5.min.js\"></script>\r\n+\r\n+    <script type=\"text/javascript\" src=\"js/mustache.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/helpers.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/crono.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/app.js\"></script>\r\n+\r\n+    <script type=\"text/javascript\" src=\"js/hardware.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/invelion.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/chafon.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/reader1.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/reader2.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/reader3.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/chafon_810.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/reader_zebra_fx9600.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic1.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic2.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic3.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic4.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic5.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/cronopic6.js\"></script>\r\n+    <script type=\"text/javascript\" src=\"js/hardwares/fotocelula1.js\"></script>\r\n+\r\n+    <script type=\"text/javascript\" src=\"cordova.js\"></script>\r\n+\r\n+    <script>\r\n+        var enter = true;\r\n+        app.initialize();\r\n+        hardware.initialize();\r\n+\r\n+        // Funciones de ayuda para el modo test del Zebra FX9600\r\n+        window.zebraTest = {\r\n+            activar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.activarModoTest();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            desactivar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.desactivarModoTest();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            estado: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🔍 Estado del reader Zebra FX9600:');\r\n+                    console.log('   🧪 Modo test:', reader_zebra_fx9600.testMode);\r\n+                    console.log('   ⏸️ Paso a paso:', reader_zebra_fx9600.stepByStep);\r\n+                    console.log('   🔗 Conectado:', reader_zebra_fx9600.isConnected);\r\n+                    console.log('   📡 Antena actual:', reader_zebra_fx9600.antena);\r\n+                    console.log('   📊 Modo RFID:', window.rfid_crono);\r\n+                    console.log('   📡 Antenas habilitadas:');\r\n+                    for (let i = 1; i <= 8; i++) {\r\n+                        console.log('      Antena ' + i + ':', window['antena_' + i] ? '✅' : '❌');\r\n+                    }\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            rapida: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.inicializacionRapida();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            minima: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🚀 INICIALIZACIÓN MÍNIMA - Solo ROSpec');\r\n+\r\n+                    // Verificar conexión\r\n+                    if (!reader_zebra_fx9600.connection || !reader_zebra_fx9600.isConnected) {\r\n+                        console.log('❌ No hay conexión activa. Ejecuta zebraTest.reconectar() primero');\r\n+                        return;\r\n+                    }\r\n+\r\n+                    // Usar ROSpec ID diferente para evitar conflictos\r\n+                    let originalId = reader_zebra_fx9600.rospecId;\r\n+                    reader_zebra_fx9600.rospecId = Math.floor(Math.random() * 1000) + 100;\r\n+                    console.log('🔧 Usando ROSpec ID:', reader_zebra_fx9600.rospecId);\r\n+\r\n+                    reader_zebra_fx9600.configurarROSpecMinimo().then(() => {\r\n+                        console.log('✅ Inicialización mínima completada');\r\n+                        reader_zebra_fx9600.finalizarInicializacion();\r\n+                    }).catch((error) => {\r\n+                        console.error('❌ Error en inicialización mínima:', error);\r\n+                        reader_zebra_fx9600.rospecId = originalId; // Restaurar ID original\r\n+                    });\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            habilitar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('✅ HABILITANDO procesamiento de tags');\r\n+                    reader_zebra_fx9600.rospecActivo = true;\r\n+                    reader_zebra_fx9600.finalizarInicializacion();\r\n+                    console.log('🎉 Reader listo para procesar tags');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            reconectar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.reconectar();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            debug: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🐛 ACTIVANDO LOGS DE DEBUG...');\r\n+                    reader_zebra_fx9600.debugMode = true;\r\n+                    console.log('✅ Logs de debug activados - ahora se mostrarán todos los logs');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            watchdog: function() {\r\n+                console.log('🐕 INICIANDO WATCHDOG para detectar bloqueos...');\r\n+                let counter = 0;\r\n+                let lastTime = Date.now();\r\n+\r\n+                const watchdogInterval = setInterval(() => {\r\n+                    const now = Date.now();\r\n+                    const elapsed = now - lastTime;\r\n+\r\n+                    if (elapsed > 2000) { // Si pasan más de 2 segundos\r\n+                        console.error('🚨 BLOQUEO DETECTADO! Hilo principal bloqueado por', elapsed, 'ms');\r\n+                        console.error('🚨 Último contador:', counter);\r\n+                        console.error('🚨 Posible causa: operación síncrona o await sin timeout');\r\n+                    }\r\n+\r\n+                    lastTime = now;\r\n+                    counter++;\r\n+\r\n+                    if (counter % 10 === 0) {\r\n+                        console.log('🐕 Watchdog activo - contador:', counter);\r\n+                    }\r\n+                }, 1000);\r\n+\r\n+                // Detener después de 2 minutos\r\n+                setTimeout(() => {\r\n+                    clearInterval(watchdogInterval);\r\n+                    console.log('🐕 Watchdog detenido');\r\n+                }, 120000);\r\n+            },\r\n+            ayuda: function() {\r\n+                console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n+                console.log('   zebraTest.activar()    - Activa el modo test');\r\n+                console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n+                console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n+                console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n+                console.log('   zebraTest.rapida()     - Inicialización rápida (BLOQUEADA)');\r\n+                console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n+                console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n+                console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n+                console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n+                console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n+            }\r\n+        };\r\n+\r\n+        // Mostrar ayuda al cargar\r\n+        console.log('🧪 ZEBRA FX9600 TEST MODE DISPONIBLE');\r\n+        console.log('💡 Escribe \"zebraTest.ayuda()\" en la consola para ver los comandos disponibles');\r\n+\r\n+    </script>\r\n+    <!-- End Scripts -->\r\n+\r\n+</head>\r\n+\r\n+<body>\r\n+\r\n+    <div id=\"crono-btn\">\r\n+        <a class=\"ui-btn\" href=\"#\" onclick=\"app.teclado('crono')\" data-icon=\"check\" style=\"color: #f7931e;\" id=\"crono\">\r\n+            <img src=\"images/isotipo.png\">\r\n+        </a>\r\n+    </div>\r\n+\r\n+    <div data-role=\"footer\" data-position=\"fixed\" class=\"ui-footer ui-bar-a ui-footer-fixed slideup ui-body ui-body-b\">\r\n+        <button onclick=\"app.teclado('config')\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-gear\">Config</button>\r\n+        <button onclick=\"app.teclado('reenviar')\" id=\"reenviar-btn\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-refresh\">Sincro&nbsp;(<span id=\"reenviar-cantidad\"></span>)</button>\r\n+    </div>\r\n+\r\n+    <div data-role=\"page\" id=\"lecturas\">\r\n+        <div data-role=\"content\">\r\n+\r\n+            <div class=\"ui-grid-a ui-corner-all\" style=\"font-size: 22px;\">\r\n+              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"reloj\">00:00:00</div></div>\r\n+              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"tiempo_pena\">-</div></div>\r\n+              <div class=\"ui-block-b\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a\" id=\"idparticipante\" style=\"color: #f7931e;\">-</div></div>\r\n+              <div style=\"display: none;\" id=\"tagID\"></div>\r\n+            </div>\r\n+\r\n+            <div id=\"penas\" style=\"text-align: center;\"></div>\r\n+\r\n+            <div id=\"crono-keys\">\r\n+                <div class=\"ui-grid-c ui-corner-all\">\r\n+                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('buscador')\" data-icon=\"search\" id=\"buscador-btn\"></a></div>\r\n+                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('1')\" alt=\"1\">1</a></div>\r\n+                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('2')\" alt=\"2\">2</a></div>\r\n+                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('3')\" alt=\"3\">3</a></div>\r\n+                </div>\r\n+                <div class=\"ui-grid-c ui-corner-all\">\r\n+                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('scanqr')\" data-icon=\"camera\" id=\"qr\"></a></div>\r\n+                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('4')\" alt=\"4\">4</a></div>\r\n+                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('5')\" alt=\"5\">5</a></div>\r\n+                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('6')\" alt=\"6\">6</a></div>\r\n+                </div>\r\n+                <div class=\"ui-grid-c ui-corner-all\">\r\n+                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('esperar')\" data-icon=\"plus\" id=\"esperar\"></a></div>\r\n+                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('7')\" alt=\"7\">7</a></div>\r\n+                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('8')\" alt=\"8\">8</a></div>\r\n+                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('9')\" alt=\"9\">9</a></div>\r\n+                </div>\r\n+                <div class=\"ui-grid-c ui-corner-all\">\r\n+                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('multicrono')\" data-icon=\"check\" style=\"height: 25px;\" id=\"multicrono\"></a></div>\r\n+                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('backspace')\" data-icon=\"arrow-l\" style=\"height: 25px;\"></a></div>\r\n+                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('0')\" alt=\"0\">0</a></div>\r\n+                    <br>\r\n+                </div>\r\n+\r\n+                <div id=\"pena\" class=\"ui-grid-a ui-corner-all\">\r\n+                    <a data-role=\"button\" href=\"#\" onclick=\"app.teclado('pena')\" data-icon=\"check\" style=\"color: #f7931e;\">PENA</a>\r\n+                    <br>\r\n+                </div>\r\n+\r\n+                <div id=\"buscador\" class=\"ui-grid-a ui-corner-all\" style=\"text-align: center; display: none;\">\r\n+                    <ul id=\"buscar\" data-role=\"listview\" data-inset=\"true\" data-filter=\"true\" data-filter-placeholder=\"\" data-filter-theme=\"a\"></ul>\r\n+                    <div id=\"buscando-gif\" style=\"display: none;\"><img src=\"images/ajax-loader-2.gif\"></div>\r\n+                    <br>\r\n+                </div>\r\n+\r\n+            </div>\r\n+\r\n+            <div id=\"totales\" class=\"ui-grid-a ui-corner-all\"></div>\r\n+            <div id=\"consola\"></div>\r\n+\r\n+            <br/>\r\n+\r\n+            <ul data-role=\"listview\" class=\"ui-listview\" id=\"listaParticipantes\"></ul>\r\n+\r\n+            <br/><br/><br/><br/><br/><br/><br/>\r\n+\r\n+        </div>\r\n+\r\n+        <div data-role=\"popup\" id=\"popup\" data-theme=\"a\" class=\"ui-corner-all\"></div>\r\n+        <a id=\"descargarLog\" style=\"display:none\"></a>\r\n+\r\n+    </div>\r\n+\r\n+    <!-- Start of second page -->\r\n+    <div data-role=\"page\" id=\"kiosko\">\r\n+        <iframe src=\"https://cronometrajeinstantaneo.com/resultados/vuelta-de-obligado-vob-etapa-3-circuito-owa-2324/ticket?idparticipante=153\" frameborder=\"0\" width=\"100%\" height=\"650px\"></iframe>\r\n+    </div>\r\n+\r\n+\r\n+    <!-- Templates -->\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-penas\">\r\n+        {{# opciones_penas }}\r\n+        <a data-role=\"button\" class=\"ui-link ui-btn ui-btn-inline ui-shadow ui-corner-all  ui-btn-b\" href=\"#\" onclick=\"app.opcionPena('{{ opcion_pena }}')\" alt=\"{{ opcion_pena }}\">{{ opcion_pena }}</a>\r\n+        {{/ opciones_penas }}\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-li\">\r\n+        <li class=\"ui-li ui-li-static ui-body-c\" id=\"{{ idcrono }}\">{{ mensaje }}</li>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-totales\">\r\n+        <div class=\"ui-body ui-bar ui-bar-a text-center\">\r\n+            Bar: {{ barrido }} | Tot: {{ total }} | Dif: {{ diff }} | Últ: {{ ultimo }}\r\n+            {{# antenas }}\r\n+            <br>\r\n+            1: {{ antena_1 }} | 2: {{ antena_2 }} | 3: {{ antena_3 }} | 4: {{ antena_4 }} |\r\n+            5: {{ antena_5 }} | 6: {{ antena_6 }} | 7: {{ antena_7 }} | 8: {{ antena_8 }}\r\n+            {{/ antenas }}\r\n+        </div>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-control\">\r\n+        <span class=\"evento\">{{ evento }}</span><br>\r\n+        <span class=\"codigo\"><b>{{ codigo }}</b></span>\r\n+        - <span class=\"nombre\">{{ nombre }}</span>\r\n+        <span class=\"tipo\">({{ tipo }})</span>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-cargando\">\r\n+        <a href=\"#\" onclick=\"app.cancel($(this))\" class=\"ui-icon-delete ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n+            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n+        </a>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"hora\">{{ hora }}</span>\r\n+        - <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-lectura\">\r\n+        {{^ extra }}\r\n+        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"ui-icon-edit ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n+            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n+        </a>\r\n+        {{/ extra }}\r\n+        {{# extra }}\r\n+        <a href=\"#\" class=\"ui-icon-check ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n+        </a>\r\n+        {{/ extra }}\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n+        {{# extra }}\r\n+        - <span class=\"extra\">{{ extra }}</span>\r\n+        {{/ extra }}\r\n+        {{^ extra }}\r\n+        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n+        {{/ extra }}\r\n+        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"hora\">{{ hora }}</span>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-baja\">\r\n+        <a href=\"#\" class=\"ui-icon-noicon ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n+            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n+        </a>\r\n+        <del>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n+        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n+        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"hora\">{{ hora }}</span>\r\n+        </del>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-error\">\r\n+        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"re-enviar ui-icon-edit ui-btn-icon-left\" id=\"{{ idcrono }}\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+        </a>\r\n+        <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n+        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"hora\">{{ hora }}</span>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-default\">\r\n+        <span class=\"json\">ERROR: {{ json }}</span>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-esperando\">\r\n+        <a href=\"#\" onclick=\"app.esperando($(this))\" class=\"esperando ui-icon-check ui-btn-icon-left\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+        </a>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n+        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n+        <span class=\"tiempo\"> </span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-pena\">\r\n+        <a href=\"#\" onclick=\"\" class=\"ui-icon-delee ui-btn-icon-left\" id=\"{{ idpena }}\">\r\n+            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n+            <span class=\"idcambiotiempo\" style=\"display: none;\">{{ idcambiotiempo }}</span>\r\n+        </a>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n+        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"tiempo\">{{ tiempo }} seg</span>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-tag\">\r\n+        <a href=\"#\" onclick=\"\" class=\"ui-icon-check ui-btn-icon-left\">\r\n+        </a>\r\n+        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n+        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n+        <span class=\"contador\">{{ idcrono }}</span>\r\n+        - <span class=\"resultado\">{{ resultado }}</span>\r\n+        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n+        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n+            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n+        </a>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-edit\">\r\n+    <div id=\"editForm\">\r\n+        <div style=\"padding:10px 20px; text-align: center;\">\r\n+            <input type=\"hidden\" name=\"idcrono\" value=\"{{ idcrono }}\" />\r\n+            <input type=\"hidden\" name=\"idlectura\" value=\"{{ idlectura }}\" />\r\n+            <input type=\"hidden\" name=\"resultado\" value=\"{{ resultado }}\" />\r\n+            <input type=\"hidden\" name=\"hora\" value=\"{{ hora }}\" />\r\n+            <input type=\"hidden\" name=\"tiempo\" value=\"{{ tiempo }}\" />\r\n+            <input type=\"hidden\" name=\"tiempo_listo_original\" value=\"{{ tiempo_listo }}\" />\r\n+\r\n+            <label>Participante:<br />\r\n+                <input name=\"idparticipante\" type=\"number\" pattern=\"[0-9]*\" inputmode=\"numeric\" value=\"{{ idparticipante }}\" style=\"width:100px;\" onClick=\"this.select();\" />\r\n+            </label>\r\n+\r\n+            <label>Hora:<br />\r\n+                <input name=\"hora_1\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_1 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n+                <b>:</b>\r\n+                <input name=\"hora_2\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_2 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n+                <b>:</b>\r\n+                <input name=\"hora_3\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_3 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n+                <b>.</b>\r\n+                <input name=\"hora_4\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{3}\" onchange=\"app.changeHora()\" value=\"{{ hora_4 }}\" inputmode=\"numeric\" style=\"width:70px;\"  onClick=\"this.select();\" />\r\n+            </label>\r\n+\r\n+            {{ #idlectura }}\r\n+            <label>Tiempo:<br />\r\n+                <input type=\"text\" name=\"tiempo_listo\" onchange=\"app.changeTiempo()\" value=\"{{ tiempo_listo }}\" disabled />\r\n+            </label>\r\n+            {{ /idlectura }}\r\n+            {{ ^idlectura }}\r\n+                <input type=\"hidden\" name=\"tiempo_listo\" value=\"{{ tiempo_listo }}\" />\r\n+            {{ /idlectura }}\r\n+\r\n+            <a onclick=\"app.menos();\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-d ui-btn-icon-notext ui-btn-inline\"></a>\r\n+            <a onclick=\"app.mas()\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-u ui-btn-icon-notext ui-btn-inline\"></a><br />\r\n+\r\n+            <button type=\"submit\" onclick=\"app.ok()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Modificar</button>\r\n+            <button type=\"submit\" onclick=\"app.baja()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Borrar</button>\r\n+        </div>\r\n+    </div>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-configurar\">\r\n+    <div id=\"configurarForm\">\r\n+        <div style=\"padding:10px 20px; text-align: center;\">\r\n+            <label>Código del Control:<br />\r\n+                <input type=\"text\" name=\"codigo\" value=\"{{ codigo }}\" maxlength=\"4\" />\r\n+            </label>\r\n+            <label>Cronometrador:<br />\r\n+                <input type=\"text\" name=\"cronometrador\" value=\"{{ cronometrador }}\" maxlength=\"40\" />\r\n+            </label>\r\n+\r\n+            {{ #compatible_nube_hibrida }}\r\n+            <label>Nube híbrida:<br />\r\n+                <input type=\"checkbox\" name=\"nube_hibrida\" {{ nube_hibrida }}/>\r\n+            </label>\r\n+            {{ /compatible_nube_hibrida }}\r\n+\r\n+            <button type=\"submit\" onclick=\"app.configurar()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Configurar</button>\r\n+\r\n+            <hr>\r\n+\r\n+            {{ #compatible_hardware }}\r\n+            <label>Hardware configurado:<br />\r\n+                {{ conectar_hardware }}\r\n+            </label>\r\n+\r\n+            <button type=\"submit\" onclick=\"app.configHardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Hardware</button>\r\n+\r\n+            {{ /compatible_hardware }}\r\n+\r\n+            <p style=\"font-size: small\">v {{ version }}</p>\r\n+        </div>\r\n+    </div>\r\n+    </script>\r\n+\r\n+    <script type=\"x-tmpl-mustache\" id=\"tmpl-hardware\">\r\n+    <div id=\"hardwareForm\">\r\n+        <div style=\"padding:10px 20px; text-align: center;\">\r\n+\r\n+            <h4>Dispositivo 1</h4>\r\n+            <select name=\"dispositivo1\">\r\n+            {{ #android }}\r\n+                <option value=\"\">No conectado</option>\r\n+                <option value=\"cronopic4\">Fotocélula Bluetooth</option>\r\n+                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n+            {{ /android }}\r\n+            {{ #windows }}\r\n+                <option value=\"\">No conectado</option>\r\n+                <option value=\"reader1\">Reader UHF RFID por USB</option>\r\n+                <option value=\"reader2\">Reader UHF RFID por LAN</option>\r\n+                <option value=\"reader3\">USB Desktop UHF RFID</option>\r\n+                <option value=\"chafon_810\">Reader Chafon 810 por LAN</option>\r\n+                <option value=\"reader_zebra_fx9600\">Zebra FX9600 RFID Reader (LLRP)</option>\r\n+                <option value=\"cronopic4\">Fotocélula Cronopic Bluetooth</option>\r\n+                <option value=\"cronopic3\">Fotocélula Cronopic Wifi Multicanal</option>\r\n+                <option value=\"cronopic1\">Fotocélula Cronopic Wifi CH1</option>\r\n+                <option value=\"cronopic2\">Fotocélula Cronopic Wifi CH2</option>\r\n+                <option value=\"cronopic5\">Fotocélula Cronopic con Reloj Interno</option>\r\n+                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n+                <option value=\"fotocelula1\">Fotocélula Genérica por Espacio</option>\r\n+            {{ /windows }}\r\n+            </select>\r\n+            <br />\r\n+            {{ #windows }}\r\n+            <select name=\"puerto1\">\r\n+                <option value=\"\">No utilizar COM</option>\r\n+                <option value=\"COM1\">COM1</option>\r\n+                <option value=\"COM2\">COM2</option>\r\n+                <option value=\"COM3\">COM3</option>\r\n+                <option value=\"COM4\">COM4</option>\r\n+                <option value=\"COM5\">COM5</option>\r\n+                <option value=\"COM6\">COM6</option>\r\n+                <option value=\"COM7\">COM7</option>\r\n+                <option value=\"COM8\">COM8</option>\r\n+                <option value=\"COM9\">COM9</option>\r\n+                <option value=\"COM10\">COM10</option>\r\n+                <option value=\"COM11\">COM11</option>\r\n+                <option value=\"COM12\">COM12</option>\r\n+                <option value=\"COM13\">COM13</option>\r\n+                <option value=\"COM14\">COM14</option>\r\n+                <option value=\"COM15\">COM15</option>\r\n+                <option value=\"COM16\">COM16</option>\r\n+                <option value=\"COM17\">COM17</option>\r\n+                <option value=\"COM18\">COM18</option>\r\n+                <option value=\"COM19\">COM19</option>\r\n+                <option value=\"COM20\">COM20</option>\r\n+            </select>\r\n+            <br />\r\n+            {{ /windows }}\r\n+            {{ #linux  }}\r\n+            <select name=\"puerto1\">\r\n+                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n+                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n+                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n+                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n+            </select>\r\n+            <br />\r\n+            {{ /linux  }}\r\n+            </select>\r\n+            <br />\r\n+            {{ #windows }}\r\n+            <h4>Configuración de reader</h4>\r\n+\r\n+            <label>Modo:\r\n+            <select name=\"rfid_crono\" onchange=\"isTagCodigo()\">\r\n+                <option value=\"test\">Pruebas</option>\r\n+                <option value=\"real\">Cronometraje Real Time</option>\r\n+                <option value=\"fast\">Cronometraje Fast Switch</option>\r\n+                <option value=\"tag\">Asignar chip</option>\r\n+                <option value=\"tag_codigo\">Grabar chip</option>\r\n+            </select>\r\n+            </label>\r\n+\r\n+            <div id=\"tag_codigo\" style=\"display: none\">\r\n+                <label>Prefijo <input type=\"text\" name=\"tag_prefijo\" maxlength=\"8\" style=\"width: 100px;\" /></label>\r\n+            </div>\r\n+\r\n+            <label>Ignorar repetidos:\r\n+            <select name=\"rebote\">\r\n+                <option value=\"0\">Nunca</option>\r\n+                <option value=\"5000\">5 seg</option>\r\n+                <option value=\"30000\">30 seg</option>\r\n+                <option value=\"300000\">5 min</option>\r\n+                <option value=\"1800000\">30 min</option>\r\n+                <option value=\"86400000\">24 hs</option>\r\n+            </select>\r\n+            </label>\r\n+\r\n+            <!--\r\n+            <label>Intervalo de lectura:\r\n+            <select name=\"intervalo\">\r\n+                <option value=\"25\">25 ms</option>\r\n+                <option value=\"50\">50 ms</option>\r\n+                <option value=\"100\">100 ms</option>\r\n+                <option value=\"250\">250 ms</option>\r\n+                <option value=\"500\">500 ms</option>\r\n+                <option value=\"1000\">1 seg</option>\r\n+            </select>\r\n+            </label>\r\n+            -->\r\n+\r\n+            <label>Reproducir sonido: <input type=\"checkbox\" name=\"sonido\"/></label>\r\n+\r\n+            <h4>Antenas conectadas:</h4>\r\n+            <label style=\"display: inline;\">1 <input type=\"checkbox\" name=\"antena_1\"/></label>\r\n+            <label style=\"display: inline;\">2 <input type=\"checkbox\" name=\"antena_2\"/></label>\r\n+            <label style=\"display: inline;\">3 <input type=\"checkbox\" name=\"antena_3\"/></label>\r\n+            <label style=\"display: inline;\">4 <input type=\"checkbox\" name=\"antena_4\"/></label>\r\n+            <br />\r\n+            <label style=\"display: inline;\">5 <input type=\"checkbox\" name=\"antena_5\"/></label>\r\n+            <label style=\"display: inline;\">6 <input type=\"checkbox\" name=\"antena_6\"/></label>\r\n+            <label style=\"display: inline;\">7 <input type=\"checkbox\" name=\"antena_7\"/></label>\r\n+            <label style=\"display: inline;\">8 <input type=\"checkbox\" name=\"antena_8\"/></label>\r\n+\r\n+            <br />\r\n+            {{ /windows }}\r\n+            {{ #linux  }}\r\n+            <select name=\"puerto2\">\r\n+                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n+                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n+                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n+                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n+            </select>\r\n+            <br />\r\n+            {{ /linux  }}\r\n+\r\n+            <br />\r\n+\r\n+            <button type=\"submit\" onclick=\"app.hardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Configurar</button>\r\n+        </div>\r\n+    </div>\r\n+    </script>\r\n+    <!-- End Templates -->\r\n+\r\n+</body>\r\n+</html>\r\n"}, {"date": 1756923616431, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -119,8 +119,15 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            consultar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.consultarROSpecs();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756923667978, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -180,8 +180,9 @@\n                 console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n                 console.log('   zebraTest.rapida()     - Inicialización rápida (BLOQUEADA)');\r\n                 console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n+                console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n"}, {"date": 1756923961064, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -126,8 +126,15 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            activar: function(rospecId = 1) {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    reader_zebra_fx9600.activarROSpec(rospecId);\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756924006518, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -188,8 +188,9 @@\n                 console.log('   zebraTest.rapida()     - Inicialización rápida (BLOQUEADA)');\r\n                 console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n+                console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n             }\r\n@@ -664,655 +665,4 @@\n     <!-- End Templates -->\r\n \r\n </body>\r\n </html>\r\n-<!DOCTYPE html>\r\n-<html>\r\n-<head>\r\n-    <title>Cronometraje Instantaneo</title>\r\n-    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\r\n-    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\" >\r\n-    <link rel=\"stylesheet\" href=\"css/jquery.mobile-1.4.5.min.css\" />\r\n-    <link rel=\"stylesheet\" href=\"css/jquery.mobile-crono.css\" />\r\n-    <link rel=\"stylesheet\" href=\"css/app.css\" />\r\n-    <link rel=\"shortcut icon\" href=\"favicon.ico\">\r\n-\r\n-\r\n-    <!-- Insert this line above script imports  -->\r\n-    <script>if (typeof module === 'object') {window.module = module; module = undefined;}</script>\r\n-\r\n-    <!-- Scripts -->\r\n-    <script type=\"text/javascript\" src=\"js/jquery-2.1.1.min.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/jquery.mobile-1.4.5.min.js\"></script>\r\n-\r\n-    <script type=\"text/javascript\" src=\"js/mustache.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/helpers.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/crono.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/app.js\"></script>\r\n-\r\n-    <script type=\"text/javascript\" src=\"js/hardware.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/invelion.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/chafon.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/reader1.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/reader2.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/reader3.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/chafon_810.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/reader_zebra_fx9600.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic1.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic2.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic3.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic4.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic5.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/cronopic6.js\"></script>\r\n-    <script type=\"text/javascript\" src=\"js/hardwares/fotocelula1.js\"></script>\r\n-\r\n-    <script type=\"text/javascript\" src=\"cordova.js\"></script>\r\n-\r\n-    <script>\r\n-        var enter = true;\r\n-        app.initialize();\r\n-        hardware.initialize();\r\n-\r\n-        // Funciones de ayuda para el modo test del Zebra FX9600\r\n-        window.zebraTest = {\r\n-            activar: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    reader_zebra_fx9600.activarModoTest();\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            desactivar: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    reader_zebra_fx9600.desactivarModoTest();\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            estado: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🔍 Estado del reader Zebra FX9600:');\r\n-                    console.log('   🧪 Modo test:', reader_zebra_fx9600.testMode);\r\n-                    console.log('   ⏸️ Paso a paso:', reader_zebra_fx9600.stepByStep);\r\n-                    console.log('   🔗 Conectado:', reader_zebra_fx9600.isConnected);\r\n-                    console.log('   📡 Antena actual:', reader_zebra_fx9600.antena);\r\n-                    console.log('   📊 Modo RFID:', window.rfid_crono);\r\n-                    console.log('   📡 Antenas habilitadas:');\r\n-                    for (let i = 1; i <= 8; i++) {\r\n-                        console.log('      Antena ' + i + ':', window['antena_' + i] ? '✅' : '❌');\r\n-                    }\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            rapida: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    reader_zebra_fx9600.inicializacionRapida();\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            minima: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🚀 INICIALIZACIÓN MÍNIMA - Solo ROSpec');\r\n-\r\n-                    // Verificar conexión\r\n-                    if (!reader_zebra_fx9600.connection || !reader_zebra_fx9600.isConnected) {\r\n-                        console.log('❌ No hay conexión activa. Ejecuta zebraTest.reconectar() primero');\r\n-                        return;\r\n-                    }\r\n-\r\n-                    // Usar ROSpec ID diferente para evitar conflictos\r\n-                    let originalId = reader_zebra_fx9600.rospecId;\r\n-                    reader_zebra_fx9600.rospecId = Math.floor(Math.random() * 1000) + 100;\r\n-                    console.log('🔧 Usando ROSpec ID:', reader_zebra_fx9600.rospecId);\r\n-\r\n-                    reader_zebra_fx9600.configurarROSpecMinimo().then(() => {\r\n-                        console.log('✅ Inicialización mínima completada');\r\n-                        reader_zebra_fx9600.finalizarInicializacion();\r\n-                    }).catch((error) => {\r\n-                        console.error('❌ Error en inicialización mínima:', error);\r\n-                        reader_zebra_fx9600.rospecId = originalId; // Restaurar ID original\r\n-                    });\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            habilitar: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('✅ HABILITANDO procesamiento de tags');\r\n-                    reader_zebra_fx9600.rospecActivo = true;\r\n-                    reader_zebra_fx9600.finalizarInicializacion();\r\n-                    console.log('🎉 Reader listo para procesar tags');\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            reconectar: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    reader_zebra_fx9600.reconectar();\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            debug: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🐛 ACTIVANDO LOGS DE DEBUG...');\r\n-                    reader_zebra_fx9600.debugMode = true;\r\n-                    console.log('✅ Logs de debug activados - ahora se mostrarán todos los logs');\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n-            watchdog: function() {\r\n-                console.log('🐕 INICIANDO WATCHDOG para detectar bloqueos...');\r\n-                let counter = 0;\r\n-                let lastTime = Date.now();\r\n-\r\n-                const watchdogInterval = setInterval(() => {\r\n-                    const now = Date.now();\r\n-                    const elapsed = now - lastTime;\r\n-\r\n-                    if (elapsed > 2000) { // Si pasan más de 2 segundos\r\n-                        console.error('🚨 BLOQUEO DETECTADO! Hilo principal bloqueado por', elapsed, 'ms');\r\n-                        console.error('🚨 Último contador:', counter);\r\n-                        console.error('🚨 Posible causa: operación síncrona o await sin timeout');\r\n-                    }\r\n-\r\n-                    lastTime = now;\r\n-                    counter++;\r\n-\r\n-                    if (counter % 10 === 0) {\r\n-                        console.log('🐕 Watchdog activo - contador:', counter);\r\n-                    }\r\n-                }, 1000);\r\n-\r\n-                // Detener después de 2 minutos\r\n-                setTimeout(() => {\r\n-                    clearInterval(watchdogInterval);\r\n-                    console.log('🐕 Watchdog detenido');\r\n-                }, 120000);\r\n-            },\r\n-            ayuda: function() {\r\n-                console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n-                console.log('   zebraTest.activar()    - Activa el modo test');\r\n-                console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n-                console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n-                console.log('   zebraTest.reconectar() - Reconecta al reader');\r\n-                console.log('   zebraTest.rapida()     - Inicialización rápida (BLOQUEADA)');\r\n-                console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n-                console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n-                console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n-                console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n-            }\r\n-        };\r\n-\r\n-        // Mostrar ayuda al cargar\r\n-        console.log('🧪 ZEBRA FX9600 TEST MODE DISPONIBLE');\r\n-        console.log('💡 Escribe \"zebraTest.ayuda()\" en la consola para ver los comandos disponibles');\r\n-\r\n-    </script>\r\n-    <!-- End Scripts -->\r\n-\r\n-</head>\r\n-\r\n-<body>\r\n-\r\n-    <div id=\"crono-btn\">\r\n-        <a class=\"ui-btn\" href=\"#\" onclick=\"app.teclado('crono')\" data-icon=\"check\" style=\"color: #f7931e;\" id=\"crono\">\r\n-            <img src=\"images/isotipo.png\">\r\n-        </a>\r\n-    </div>\r\n-\r\n-    <div data-role=\"footer\" data-position=\"fixed\" class=\"ui-footer ui-bar-a ui-footer-fixed slideup ui-body ui-body-b\">\r\n-        <button onclick=\"app.teclado('config')\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-gear\">Config</button>\r\n-        <button onclick=\"app.teclado('reenviar')\" id=\"reenviar-btn\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-refresh\">Sincro&nbsp;(<span id=\"reenviar-cantidad\"></span>)</button>\r\n-    </div>\r\n-\r\n-    <div data-role=\"page\" id=\"lecturas\">\r\n-        <div data-role=\"content\">\r\n-\r\n-            <div class=\"ui-grid-a ui-corner-all\" style=\"font-size: 22px;\">\r\n-              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"reloj\">00:00:00</div></div>\r\n-              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"tiempo_pena\">-</div></div>\r\n-              <div class=\"ui-block-b\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a\" id=\"idparticipante\" style=\"color: #f7931e;\">-</div></div>\r\n-              <div style=\"display: none;\" id=\"tagID\"></div>\r\n-            </div>\r\n-\r\n-            <div id=\"penas\" style=\"text-align: center;\"></div>\r\n-\r\n-            <div id=\"crono-keys\">\r\n-                <div class=\"ui-grid-c ui-corner-all\">\r\n-                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('buscador')\" data-icon=\"search\" id=\"buscador-btn\"></a></div>\r\n-                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('1')\" alt=\"1\">1</a></div>\r\n-                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('2')\" alt=\"2\">2</a></div>\r\n-                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('3')\" alt=\"3\">3</a></div>\r\n-                </div>\r\n-                <div class=\"ui-grid-c ui-corner-all\">\r\n-                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('scanqr')\" data-icon=\"camera\" id=\"qr\"></a></div>\r\n-                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('4')\" alt=\"4\">4</a></div>\r\n-                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('5')\" alt=\"5\">5</a></div>\r\n-                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('6')\" alt=\"6\">6</a></div>\r\n-                </div>\r\n-                <div class=\"ui-grid-c ui-corner-all\">\r\n-                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('esperar')\" data-icon=\"plus\" id=\"esperar\"></a></div>\r\n-                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('7')\" alt=\"7\">7</a></div>\r\n-                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('8')\" alt=\"8\">8</a></div>\r\n-                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('9')\" alt=\"9\">9</a></div>\r\n-                </div>\r\n-                <div class=\"ui-grid-c ui-corner-all\">\r\n-                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('multicrono')\" data-icon=\"check\" style=\"height: 25px;\" id=\"multicrono\"></a></div>\r\n-                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('backspace')\" data-icon=\"arrow-l\" style=\"height: 25px;\"></a></div>\r\n-                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('0')\" alt=\"0\">0</a></div>\r\n-                    <br>\r\n-                </div>\r\n-\r\n-                <div id=\"pena\" class=\"ui-grid-a ui-corner-all\">\r\n-                    <a data-role=\"button\" href=\"#\" onclick=\"app.teclado('pena')\" data-icon=\"check\" style=\"color: #f7931e;\">PENA</a>\r\n-                    <br>\r\n-                </div>\r\n-\r\n-                <div id=\"buscador\" class=\"ui-grid-a ui-corner-all\" style=\"text-align: center; display: none;\">\r\n-                    <ul id=\"buscar\" data-role=\"listview\" data-inset=\"true\" data-filter=\"true\" data-filter-placeholder=\"\" data-filter-theme=\"a\"></ul>\r\n-                    <div id=\"buscando-gif\" style=\"display: none;\"><img src=\"images/ajax-loader-2.gif\"></div>\r\n-                    <br>\r\n-                </div>\r\n-\r\n-            </div>\r\n-\r\n-            <div id=\"totales\" class=\"ui-grid-a ui-corner-all\"></div>\r\n-            <div id=\"consola\"></div>\r\n-\r\n-            <br/>\r\n-\r\n-            <ul data-role=\"listview\" class=\"ui-listview\" id=\"listaParticipantes\"></ul>\r\n-\r\n-            <br/><br/><br/><br/><br/><br/><br/>\r\n-\r\n-        </div>\r\n-\r\n-        <div data-role=\"popup\" id=\"popup\" data-theme=\"a\" class=\"ui-corner-all\"></div>\r\n-        <a id=\"descargarLog\" style=\"display:none\"></a>\r\n-\r\n-    </div>\r\n-\r\n-    <!-- Start of second page -->\r\n-    <div data-role=\"page\" id=\"kiosko\">\r\n-        <iframe src=\"https://cronometrajeinstantaneo.com/resultados/vuelta-de-obligado-vob-etapa-3-circuito-owa-2324/ticket?idparticipante=153\" frameborder=\"0\" width=\"100%\" height=\"650px\"></iframe>\r\n-    </div>\r\n-\r\n-\r\n-    <!-- Templates -->\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-penas\">\r\n-        {{# opciones_penas }}\r\n-        <a data-role=\"button\" class=\"ui-link ui-btn ui-btn-inline ui-shadow ui-corner-all  ui-btn-b\" href=\"#\" onclick=\"app.opcionPena('{{ opcion_pena }}')\" alt=\"{{ opcion_pena }}\">{{ opcion_pena }}</a>\r\n-        {{/ opciones_penas }}\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-li\">\r\n-        <li class=\"ui-li ui-li-static ui-body-c\" id=\"{{ idcrono }}\">{{ mensaje }}</li>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-totales\">\r\n-        <div class=\"ui-body ui-bar ui-bar-a text-center\">\r\n-            Bar: {{ barrido }} | Tot: {{ total }} | Dif: {{ diff }} | Últ: {{ ultimo }}\r\n-            {{# antenas }}\r\n-            <br>\r\n-            1: {{ antena_1 }} | 2: {{ antena_2 }} | 3: {{ antena_3 }} | 4: {{ antena_4 }} |\r\n-            5: {{ antena_5 }} | 6: {{ antena_6 }} | 7: {{ antena_7 }} | 8: {{ antena_8 }}\r\n-            {{/ antenas }}\r\n-        </div>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-control\">\r\n-        <span class=\"evento\">{{ evento }}</span><br>\r\n-        <span class=\"codigo\"><b>{{ codigo }}</b></span>\r\n-        - <span class=\"nombre\">{{ nombre }}</span>\r\n-        <span class=\"tipo\">({{ tipo }})</span>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-cargando\">\r\n-        <a href=\"#\" onclick=\"app.cancel($(this))\" class=\"ui-icon-delete ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n-            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n-        </a>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"hora\">{{ hora }}</span>\r\n-        - <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-lectura\">\r\n-        {{^ extra }}\r\n-        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"ui-icon-edit ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n-            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n-        </a>\r\n-        {{/ extra }}\r\n-        {{# extra }}\r\n-        <a href=\"#\" class=\"ui-icon-check ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n-        </a>\r\n-        {{/ extra }}\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n-        {{# extra }}\r\n-        - <span class=\"extra\">{{ extra }}</span>\r\n-        {{/ extra }}\r\n-        {{^ extra }}\r\n-        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n-        {{/ extra }}\r\n-        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"hora\">{{ hora }}</span>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-baja\">\r\n-        <a href=\"#\" class=\"ui-icon-noicon ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n-            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n-        </a>\r\n-        <del>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n-        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n-        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"hora\">{{ hora }}</span>\r\n-        </del>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-error\">\r\n-        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"re-enviar ui-icon-edit ui-btn-icon-left\" id=\"{{ idcrono }}\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-        </a>\r\n-        <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n-        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"hora\">{{ hora }}</span>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-default\">\r\n-        <span class=\"json\">ERROR: {{ json }}</span>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-esperando\">\r\n-        <a href=\"#\" onclick=\"app.esperando($(this))\" class=\"esperando ui-icon-check ui-btn-icon-left\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-        </a>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n-        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n-        <span class=\"tiempo\"> </span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-pena\">\r\n-        <a href=\"#\" onclick=\"\" class=\"ui-icon-delee ui-btn-icon-left\" id=\"{{ idpena }}\">\r\n-            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n-            <span class=\"idcambiotiempo\" style=\"display: none;\">{{ idcambiotiempo }}</span>\r\n-        </a>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n-        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"tiempo\">{{ tiempo }} seg</span>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-tag\">\r\n-        <a href=\"#\" onclick=\"\" class=\"ui-icon-check ui-btn-icon-left\">\r\n-        </a>\r\n-        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n-        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n-        <span class=\"contador\">{{ idcrono }}</span>\r\n-        - <span class=\"resultado\">{{ resultado }}</span>\r\n-        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n-        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n-            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n-        </a>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-edit\">\r\n-    <div id=\"editForm\">\r\n-        <div style=\"padding:10px 20px; text-align: center;\">\r\n-            <input type=\"hidden\" name=\"idcrono\" value=\"{{ idcrono }}\" />\r\n-            <input type=\"hidden\" name=\"idlectura\" value=\"{{ idlectura }}\" />\r\n-            <input type=\"hidden\" name=\"resultado\" value=\"{{ resultado }}\" />\r\n-            <input type=\"hidden\" name=\"hora\" value=\"{{ hora }}\" />\r\n-            <input type=\"hidden\" name=\"tiempo\" value=\"{{ tiempo }}\" />\r\n-            <input type=\"hidden\" name=\"tiempo_listo_original\" value=\"{{ tiempo_listo }}\" />\r\n-\r\n-            <label>Participante:<br />\r\n-                <input name=\"idparticipante\" type=\"number\" pattern=\"[0-9]*\" inputmode=\"numeric\" value=\"{{ idparticipante }}\" style=\"width:100px;\" onClick=\"this.select();\" />\r\n-            </label>\r\n-\r\n-            <label>Hora:<br />\r\n-                <input name=\"hora_1\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_1 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n-                <b>:</b>\r\n-                <input name=\"hora_2\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_2 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n-                <b>:</b>\r\n-                <input name=\"hora_3\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_3 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n-                <b>.</b>\r\n-                <input name=\"hora_4\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{3}\" onchange=\"app.changeHora()\" value=\"{{ hora_4 }}\" inputmode=\"numeric\" style=\"width:70px;\"  onClick=\"this.select();\" />\r\n-            </label>\r\n-\r\n-            {{ #idlectura }}\r\n-            <label>Tiempo:<br />\r\n-                <input type=\"text\" name=\"tiempo_listo\" onchange=\"app.changeTiempo()\" value=\"{{ tiempo_listo }}\" disabled />\r\n-            </label>\r\n-            {{ /idlectura }}\r\n-            {{ ^idlectura }}\r\n-                <input type=\"hidden\" name=\"tiempo_listo\" value=\"{{ tiempo_listo }}\" />\r\n-            {{ /idlectura }}\r\n-\r\n-            <a onclick=\"app.menos();\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-d ui-btn-icon-notext ui-btn-inline\"></a>\r\n-            <a onclick=\"app.mas()\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-u ui-btn-icon-notext ui-btn-inline\"></a><br />\r\n-\r\n-            <button type=\"submit\" onclick=\"app.ok()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Modificar</button>\r\n-            <button type=\"submit\" onclick=\"app.baja()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Borrar</button>\r\n-        </div>\r\n-    </div>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-configurar\">\r\n-    <div id=\"configurarForm\">\r\n-        <div style=\"padding:10px 20px; text-align: center;\">\r\n-            <label>Código del Control:<br />\r\n-                <input type=\"text\" name=\"codigo\" value=\"{{ codigo }}\" maxlength=\"4\" />\r\n-            </label>\r\n-            <label>Cronometrador:<br />\r\n-                <input type=\"text\" name=\"cronometrador\" value=\"{{ cronometrador }}\" maxlength=\"40\" />\r\n-            </label>\r\n-\r\n-            {{ #compatible_nube_hibrida }}\r\n-            <label>Nube híbrida:<br />\r\n-                <input type=\"checkbox\" name=\"nube_hibrida\" {{ nube_hibrida }}/>\r\n-            </label>\r\n-            {{ /compatible_nube_hibrida }}\r\n-\r\n-            <button type=\"submit\" onclick=\"app.configurar()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Configurar</button>\r\n-\r\n-            <hr>\r\n-\r\n-            {{ #compatible_hardware }}\r\n-            <label>Hardware configurado:<br />\r\n-                {{ conectar_hardware }}\r\n-            </label>\r\n-\r\n-            <button type=\"submit\" onclick=\"app.configHardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Hardware</button>\r\n-\r\n-            {{ /compatible_hardware }}\r\n-\r\n-            <p style=\"font-size: small\">v {{ version }}</p>\r\n-        </div>\r\n-    </div>\r\n-    </script>\r\n-\r\n-    <script type=\"x-tmpl-mustache\" id=\"tmpl-hardware\">\r\n-    <div id=\"hardwareForm\">\r\n-        <div style=\"padding:10px 20px; text-align: center;\">\r\n-\r\n-            <h4>Dispositivo 1</h4>\r\n-            <select name=\"dispositivo1\">\r\n-            {{ #android }}\r\n-                <option value=\"\">No conectado</option>\r\n-                <option value=\"cronopic4\">Fotocélula Bluetooth</option>\r\n-                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n-            {{ /android }}\r\n-            {{ #windows }}\r\n-                <option value=\"\">No conectado</option>\r\n-                <option value=\"reader1\">Reader UHF RFID por USB</option>\r\n-                <option value=\"reader2\">Reader UHF RFID por LAN</option>\r\n-                <option value=\"reader3\">USB Desktop UHF RFID</option>\r\n-                <option value=\"chafon_810\">Reader Chafon 810 por LAN</option>\r\n-                <option value=\"reader_zebra_fx9600\">Zebra FX9600 RFID Reader (LLRP)</option>\r\n-                <option value=\"cronopic4\">Fotocélula Cronopic Bluetooth</option>\r\n-                <option value=\"cronopic3\">Fotocélula Cronopic Wifi Multicanal</option>\r\n-                <option value=\"cronopic1\">Fotocélula Cronopic Wifi CH1</option>\r\n-                <option value=\"cronopic2\">Fotocélula Cronopic Wifi CH2</option>\r\n-                <option value=\"cronopic5\">Fotocélula Cronopic con Reloj Interno</option>\r\n-                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n-                <option value=\"fotocelula1\">Fotocélula Genérica por Espacio</option>\r\n-            {{ /windows }}\r\n-            </select>\r\n-            <br />\r\n-            {{ #windows }}\r\n-            <select name=\"puerto1\">\r\n-                <option value=\"\">No utilizar COM</option>\r\n-                <option value=\"COM1\">COM1</option>\r\n-                <option value=\"COM2\">COM2</option>\r\n-                <option value=\"COM3\">COM3</option>\r\n-                <option value=\"COM4\">COM4</option>\r\n-                <option value=\"COM5\">COM5</option>\r\n-                <option value=\"COM6\">COM6</option>\r\n-                <option value=\"COM7\">COM7</option>\r\n-                <option value=\"COM8\">COM8</option>\r\n-                <option value=\"COM9\">COM9</option>\r\n-                <option value=\"COM10\">COM10</option>\r\n-                <option value=\"COM11\">COM11</option>\r\n-                <option value=\"COM12\">COM12</option>\r\n-                <option value=\"COM13\">COM13</option>\r\n-                <option value=\"COM14\">COM14</option>\r\n-                <option value=\"COM15\">COM15</option>\r\n-                <option value=\"COM16\">COM16</option>\r\n-                <option value=\"COM17\">COM17</option>\r\n-                <option value=\"COM18\">COM18</option>\r\n-                <option value=\"COM19\">COM19</option>\r\n-                <option value=\"COM20\">COM20</option>\r\n-            </select>\r\n-            <br />\r\n-            {{ /windows }}\r\n-            {{ #linux  }}\r\n-            <select name=\"puerto1\">\r\n-                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n-                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n-                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n-                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n-            </select>\r\n-            <br />\r\n-            {{ /linux  }}\r\n-            </select>\r\n-            <br />\r\n-            {{ #windows }}\r\n-            <h4>Configuración de reader</h4>\r\n-\r\n-            <label>Modo:\r\n-            <select name=\"rfid_crono\" onchange=\"isTagCodigo()\">\r\n-                <option value=\"test\">Pruebas</option>\r\n-                <option value=\"real\">Cronometraje Real Time</option>\r\n-                <option value=\"fast\">Cronometraje Fast Switch</option>\r\n-                <option value=\"tag\">Asignar chip</option>\r\n-                <option value=\"tag_codigo\">Grabar chip</option>\r\n-            </select>\r\n-            </label>\r\n-\r\n-            <div id=\"tag_codigo\" style=\"display: none\">\r\n-                <label>Prefijo <input type=\"text\" name=\"tag_prefijo\" maxlength=\"8\" style=\"width: 100px;\" /></label>\r\n-            </div>\r\n-\r\n-            <label>Ignorar repetidos:\r\n-            <select name=\"rebote\">\r\n-                <option value=\"0\">Nunca</option>\r\n-                <option value=\"5000\">5 seg</option>\r\n-                <option value=\"30000\">30 seg</option>\r\n-                <option value=\"300000\">5 min</option>\r\n-                <option value=\"1800000\">30 min</option>\r\n-                <option value=\"86400000\">24 hs</option>\r\n-            </select>\r\n-            </label>\r\n-\r\n-            <!--\r\n-            <label>Intervalo de lectura:\r\n-            <select name=\"intervalo\">\r\n-                <option value=\"25\">25 ms</option>\r\n-                <option value=\"50\">50 ms</option>\r\n-                <option value=\"100\">100 ms</option>\r\n-                <option value=\"250\">250 ms</option>\r\n-                <option value=\"500\">500 ms</option>\r\n-                <option value=\"1000\">1 seg</option>\r\n-            </select>\r\n-            </label>\r\n-            -->\r\n-\r\n-            <label>Reproducir sonido: <input type=\"checkbox\" name=\"sonido\"/></label>\r\n-\r\n-            <h4>Antenas conectadas:</h4>\r\n-            <label style=\"display: inline;\">1 <input type=\"checkbox\" name=\"antena_1\"/></label>\r\n-            <label style=\"display: inline;\">2 <input type=\"checkbox\" name=\"antena_2\"/></label>\r\n-            <label style=\"display: inline;\">3 <input type=\"checkbox\" name=\"antena_3\"/></label>\r\n-            <label style=\"display: inline;\">4 <input type=\"checkbox\" name=\"antena_4\"/></label>\r\n-            <br />\r\n-            <label style=\"display: inline;\">5 <input type=\"checkbox\" name=\"antena_5\"/></label>\r\n-            <label style=\"display: inline;\">6 <input type=\"checkbox\" name=\"antena_6\"/></label>\r\n-            <label style=\"display: inline;\">7 <input type=\"checkbox\" name=\"antena_7\"/></label>\r\n-            <label style=\"display: inline;\">8 <input type=\"checkbox\" name=\"antena_8\"/></label>\r\n-\r\n-            <br />\r\n-            {{ /windows }}\r\n-            {{ #linux  }}\r\n-            <select name=\"puerto2\">\r\n-                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n-                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n-                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n-                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n-            </select>\r\n-            <br />\r\n-            {{ /linux  }}\r\n-\r\n-            <br />\r\n-\r\n-            <button type=\"submit\" onclick=\"app.hardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Configurar</button>\r\n-        </div>\r\n-    </div>\r\n-    </script>\r\n-    <!-- End Templates -->\r\n-\r\n-</body>\r\n-</html>\r\n"}, {"date": 1756924860825, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,8 +133,24 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            completo: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🚀 PRUEBA COMPLETA - Inicialización inteligente automática');\r\n+                    console.log('📋 Configuración de antenas desde UI');\r\n+                    console.log('🔧 Detección automática de ROSpecs');\r\n+                    console.log('📞 app.crono() asíncrono habilitado');\r\n+                    console.log('✅ Listo para usar desde la interfaz normal');\r\n+\r\n+                    // Mostrar estado actual\r\n+                    reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n+                        console.log('🎉 Sistema completamente integrado');\r\n+                    });\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756924883542, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -205,11 +205,15 @@\n                 console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n+                console.log('   zebraTest.completo()   - Prueba sistema completo integrado');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n+                console.log('');\r\n+                console.log('🎯 RECOMENDADO: Usar la interfaz normal de la aplicación');\r\n+                console.log('   El reader Zebra FX9600 ahora funciona automáticamente');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756925339960, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -141,16 +141,52 @@\n                     console.log('🔧 Detección automática de ROSpecs');\r\n                     console.log('📞 app.crono() asíncrono habilitado');\r\n                     console.log('✅ Listo para usar desde la interfaz normal');\r\n \r\n-                    // Mostrar estado actual\r\n-                    reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n-                        console.log('🎉 Sistema completamente integrado');\r\n-                    });\r\n+                    // Verificar conexión antes de consultar ROSpecs\r\n+                    if (reader_zebra_fx9600.connection && reader_zebra_fx9600.isConnected) {\r\n+                        reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n+                            console.log('🎉 Sistema completamente integrado');\r\n+                        }).catch((error) => {\r\n+                            console.log('⚠️ Error consultando ROSpecs:', error.message);\r\n+                            console.log('🎉 Sistema integrado (sin conexión activa)');\r\n+                        });\r\n+                    } else {\r\n+                        console.log('⚠️ No hay conexión activa con el reader');\r\n+                        console.log('🎉 Sistema integrado (listo para conectar)');\r\n+                    }\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            diagnostico: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🔍 DIAGNÓSTICO DEL READER ZEBRA FX9600');\r\n+                    console.log('📊 Estado actual:');\r\n+                    console.log('   - isConnected:', reader_zebra_fx9600.isConnected);\r\n+                    console.log('   - connection:', reader_zebra_fx9600.connection ? 'Existe' : 'null');\r\n+                    console.log('   - rospecActivo:', reader_zebra_fx9600.rospecActivo);\r\n+                    console.log('   - rospecId:', reader_zebra_fx9600.rospecId);\r\n+                    console.log('   - testMode:', reader_zebra_fx9600.testMode);\r\n+                    console.log('   - inicializando:', reader_zebra_fx9600.inicializando);\r\n+\r\n+                    console.log('📡 Configuración de antenas:');\r\n+                    const antenas = reader_zebra_fx9600.obtenerAntenasHabilitadas();\r\n+                    console.log('   - Antenas habilitadas:', antenas);\r\n+\r\n+                    console.log('🌐 Configuración de red:');\r\n+                    console.log('   - IP configurada: Verificar en configuración de la app');\r\n+                    console.log('   - Puerto LLRP: 5084');\r\n+\r\n+                    console.log('💡 Sugerencias:');\r\n+                    console.log('   1. Verificar que el reader esté encendido');\r\n+                    console.log('   2. Verificar IP del reader (ping *************)');\r\n+                    console.log('   3. Verificar puerto LLRP (telnet ************* 5084)');\r\n+                    console.log('   4. Verificar que LLRP esté habilitado en el reader');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756925365568, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -242,14 +242,20 @@\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.completo()   - Prueba sistema completo integrado');\r\n+                console.log('   zebraTest.diagnostico() - Diagnóstico completo del sistema');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n                 console.log('🎯 RECOMENDADO: Usar la interfaz normal de la aplicación');\r\n                 console.log('   El reader Zebra FX9600 ahora funciona automáticamente');\r\n+                console.log('');\r\n+                console.log('🔧 PROBLEMAS DE CONEXIÓN:');\r\n+                console.log('   1. Ejecutar zebraTest.diagnostico() para análisis');\r\n+                console.log('   2. Verificar IP del reader en configuración');\r\n+                console.log('   3. Verificar que el reader esté encendido y en red');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756929299818, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -141,52 +141,16 @@\n                     console.log('🔧 Detección automática de ROSpecs');\r\n                     console.log('📞 app.crono() asíncrono habilitado');\r\n                     console.log('✅ Listo para usar desde la interfaz normal');\r\n \r\n-                    // Verificar conexión antes de consultar ROSpecs\r\n-                    if (reader_zebra_fx9600.connection && reader_zebra_fx9600.isConnected) {\r\n-                        reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n-                            console.log('🎉 Sistema completamente integrado');\r\n-                        }).catch((error) => {\r\n-                            console.log('⚠️ Error consultando ROSpecs:', error.message);\r\n-                            console.log('🎉 Sistema integrado (sin conexión activa)');\r\n-                        });\r\n-                    } else {\r\n-                        console.log('⚠️ No hay conexión activa con el reader');\r\n-                        console.log('🎉 Sistema integrado (listo para conectar)');\r\n-                    }\r\n+                    // Mostrar estado actual\r\n+                    reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n+                        console.log('🎉 Sistema completamente integrado');\r\n+                    });\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n-            diagnostico: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🔍 DIAGNÓSTICO DEL READER ZEBRA FX9600');\r\n-                    console.log('📊 Estado actual:');\r\n-                    console.log('   - isConnected:', reader_zebra_fx9600.isConnected);\r\n-                    console.log('   - connection:', reader_zebra_fx9600.connection ? 'Existe' : 'null');\r\n-                    console.log('   - rospecActivo:', reader_zebra_fx9600.rospecActivo);\r\n-                    console.log('   - rospecId:', reader_zebra_fx9600.rospecId);\r\n-                    console.log('   - testMode:', reader_zebra_fx9600.testMode);\r\n-                    console.log('   - inicializando:', reader_zebra_fx9600.inicializando);\r\n-\r\n-                    console.log('📡 Configuración de antenas:');\r\n-                    const antenas = reader_zebra_fx9600.obtenerAntenasHabilitadas();\r\n-                    console.log('   - Antenas habilitadas:', antenas);\r\n-\r\n-                    console.log('🌐 Configuración de red:');\r\n-                    console.log('   - IP configurada: Verificar en configuración de la app');\r\n-                    console.log('   - Puerto LLRP: 5084');\r\n-\r\n-                    console.log('💡 Sugerencias:');\r\n-                    console.log('   1. Verificar que el reader esté encendido');\r\n-                    console.log('   2. Verificar IP del reader (ping *************)');\r\n-                    console.log('   3. Verificar puerto LLRP (telnet ************* 5084)');\r\n-                    console.log('   4. Verificar que LLRP esté habilitado en el reader');\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n@@ -242,20 +206,14 @@\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.completo()   - Prueba sistema completo integrado');\r\n-                console.log('   zebraTest.diagnostico() - Diagnóstico completo del sistema');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n                 console.log('🎯 RECOMENDADO: Usar la interfaz normal de la aplicación');\r\n                 console.log('   El reader Zebra FX9600 ahora funciona automáticamente');\r\n-                console.log('');\r\n-                console.log('🔧 PROBLEMAS DE CONEXIÓN:');\r\n-                console.log('   1. Ejecutar zebraTest.diagnostico() para análisis');\r\n-                console.log('   2. Verificar IP del reader en configuración');\r\n-                console.log('   3. Verificar que el reader esté encendido y en red');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756929360869, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,24 +133,8 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n-            completo: function() {\r\n-                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🚀 PRUEBA COMPLETA - Inicialización inteligente automática');\r\n-                    console.log('📋 Configuración de antenas desde UI');\r\n-                    console.log('🔧 Detección automática de ROSpecs');\r\n-                    console.log('📞 app.crono() asíncrono habilitado');\r\n-                    console.log('✅ Listo para usar desde la interfaz normal');\r\n-\r\n-                    // Mostrar estado actual\r\n-                    reader_zebra_fx9600.consultarROSpecs().then(() => {\r\n-                        console.log('🎉 Sistema completamente integrado');\r\n-                    });\r\n-                } else {\r\n-                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n-                }\r\n-            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n@@ -205,15 +189,11 @@\n                 console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n-                console.log('   zebraTest.completo()   - Prueba sistema completo integrado');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n-                console.log('');\r\n-                console.log('🎯 RECOMENDADO: Usar la interfaz normal de la aplicación');\r\n-                console.log('   El reader Zebra FX9600 ahora funciona automáticamente');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756929700144, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,8 +133,20 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            simple: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🚀 PRUEBA SIMPLE - Secuencia que sabemos que funciona');\r\n+                    console.log('1. zebraTest.reconectar()');\r\n+                    console.log('2. zebraTest.consultar()');\r\n+                    console.log('3. zebraTest.activar(1)');\r\n+                    console.log('');\r\n+                    console.log('Ejecuta estos comandos uno por uno para verificar que funciona');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756929723183, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -201,11 +201,17 @@\n                 console.log('   zebraTest.minima()     - Inicialización mínima (solo ROSpec)');\r\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n+                console.log('   zebraTest.simple()     - Muestra secuencia simple que funciona');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n+                console.log('');\r\n+                console.log('🎯 SECUENCIA RECOMENDADA:');\r\n+                console.log('   1. zebraTest.reconectar()');\r\n+                console.log('   2. zebraTest.consultar()');\r\n+                console.log('   3. zebraTest.activar(1)');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756930059298, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -145,8 +145,30 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            auto: async function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🤖 INICIALIZACIÓN AUTOMÁTICA');\r\n+                    try {\r\n+                        console.log('1️⃣ Reconectando...');\r\n+                        await reader_zebra_fx9600.reconectar();\r\n+\r\n+                        console.log('2️⃣ Consultando ROSpecs...');\r\n+                        const estado = await reader_zebra_fx9600.consultarROSpecs();\r\n+\r\n+                        console.log('3️⃣ Activando ROSpec...');\r\n+                        await reader_zebra_fx9600.activarROSpec(1);\r\n+\r\n+                        console.log('✅ ¡Reader listo! Acerca un tag para probar.');\r\n+\r\n+                    } catch (error) {\r\n+                        console.error('❌ Error en inicialización automática:', error);\r\n+                    }\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756930074362, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -224,8 +224,9 @@\n                 console.log('   zebraTest.habilitar()  - Habilita procesamiento (reader ya conectado)');\r\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.simple()     - Muestra secuencia simple que funciona');\r\n+                console.log('   zebraTest.auto()       - Inicialización automática completa');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756930383104, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -145,26 +145,18 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n-            auto: async function() {\r\n+            auto: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     console.log('🤖 INICIALIZACIÓN AUTOMÁTICA');\r\n-                    try {\r\n-                        console.log('1️⃣ Reconectando...');\r\n-                        await reader_zebra_fx9600.reconectar();\r\n-\r\n-                        console.log('2️⃣ Consultando ROSpecs...');\r\n-                        const estado = await reader_zebra_fx9600.consultarROSpecs();\r\n-\r\n-                        console.log('3️⃣ Activando ROSpec...');\r\n-                        await reader_zebra_fx9600.activarROSpec(1);\r\n-\r\n-                        console.log('✅ ¡Reader listo! Acerca un tag para probar.');\r\n-\r\n-                    } catch (error) {\r\n-                        console.error('❌ Error en inicialización automática:', error);\r\n-                    }\r\n+                    console.log('📋 Ejecutando secuencia paso a paso...');\r\n+                    console.log('');\r\n+                    console.log('1️⃣ Ejecuta: zebraTest.reconectar()');\r\n+                    console.log('2️⃣ Luego: zebraTest.consultar()');\r\n+                    console.log('3️⃣ Finalmente: zebraTest.minima()');\r\n+                    console.log('');\r\n+                    console.log('⚠️ Ejecuta cada comando cuando termine el anterior');\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n"}, {"date": 1756930402831, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -224,9 +224,9 @@\n                 console.log('');\r\n                 console.log('🎯 SECUENCIA RECOMENDADA:');\r\n                 console.log('   1. zebraTest.reconectar()');\r\n                 console.log('   2. zebraTest.consultar()');\r\n-                console.log('   3. zebraTest.activar(1)');\r\n+                console.log('   3. zebraTest.minima()  ← CREAR ROSpec si no existe');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756931496921, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -159,8 +159,38 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            inteligente: async function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🧠 INICIALIZACIÓN INTELIGENTE');\r\n+                    console.log('🔧 Maneja automáticamente cualquier estado del reader');\r\n+\r\n+                    // Configurar modo real\r\n+                    window.rfid_crono = 'real';\r\n+\r\n+                    try {\r\n+                        // Reconectar\r\n+                        console.log('1️⃣ Reconectando...');\r\n+                        await reader_zebra_fx9600.reconectar();\r\n+\r\n+                        // Inicialización inteligente\r\n+                        console.log('2️⃣ Analizando y configurando...');\r\n+                        const exito = await reader_zebra_fx9600.inicializacionInteligente();\r\n+\r\n+                        if (exito) {\r\n+                            console.log('🎉 ¡Reader listo! Acerca un tag para probar.');\r\n+                        } else {\r\n+                            console.log('❌ Error en inicialización inteligente');\r\n+                        }\r\n+\r\n+                    } catch (error) {\r\n+                        console.error('❌ Error:', error.message);\r\n+                    }\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756931512959, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -247,8 +247,9 @@\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.simple()     - Muestra secuencia simple que funciona');\r\n                 console.log('   zebraTest.auto()       - Inicialización automática completa');\r\n+                console.log('   zebraTest.inteligente() - Inicialización inteligente (RECOMENDADO)');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756931530474, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -252,12 +252,14 @@\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n-                console.log('🎯 SECUENCIA RECOMENDADA:');\r\n-                console.log('   1. zebraTest.reconectar()');\r\n-                console.log('   2. zebraTest.consultar()');\r\n-                console.log('   3. zebraTest.minima()  ← CREAR ROSpec si no existe');\r\n+                console.log('🎯 OPCIONES RECOMENDADAS:');\r\n+                console.log('   🧠 zebraTest.inteligente() - Maneja todo automáticamente');\r\n+                console.log('   📋 O secuencia manual:');\r\n+                console.log('      1. zebraTest.reconectar()');\r\n+                console.log('      2. zebraTest.consultar()');\r\n+                console.log('      3. zebraTest.minima()');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756931696043, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -159,34 +159,37 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n-            inteligente: async function() {\r\n+            inteligente: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n-                    console.log('🧠 INICIALIZACIÓN INTELIGENTE');\r\n-                    console.log('🔧 Maneja automáticamente cualquier estado del reader');\r\n+                    console.log('🧠 INICIALIZACIÓN INTELIGENTE SIMPLE');\r\n+                    console.log('🔧 Resuelve el problema de ROSpecs duplicados');\r\n \r\n                     // Configurar modo real\r\n                     window.rfid_crono = 'real';\r\n \r\n-                    try {\r\n-                        // Reconectar\r\n-                        console.log('1️⃣ Reconectando...');\r\n-                        await reader_zebra_fx9600.reconectar();\r\n+                    console.log('📋 Ejecutando secuencia inteligente...');\r\n+                    console.log('1️⃣ zebraTest.reconectar()');\r\n+                    console.log('2️⃣ zebraTest.consultar()');\r\n+                    console.log('3️⃣ zebraTest.limpiar()');\r\n+                    console.log('');\r\n+                    console.log('⚠️ Ejecuta cada comando cuando termine el anterior');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n+            limpiar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🧹 LIMPIANDO ROSpecs duplicados...');\r\n \r\n-                        // Inicialización inteligente\r\n-                        console.log('2️⃣ Analizando y configurando...');\r\n-                        const exito = await reader_zebra_fx9600.inicializacionInteligente();\r\n+                    if (!reader_zebra_fx9600.connection) {\r\n+                        console.log('❌ No hay conexión activa. Ejecuta zebraTest.reconectar() primero');\r\n+                        return;\r\n+                    }\r\n \r\n-                        if (exito) {\r\n-                            console.log('🎉 ¡Reader listo! Acerca un tag para probar.');\r\n-                        } else {\r\n-                            console.log('❌ Error en inicialización inteligente');\r\n-                        }\r\n-\r\n-                    } catch (error) {\r\n-                        console.error('❌ Error:', error.message);\r\n-                    }\r\n+                    // Limpiar ROSpecs duplicados de forma simple\r\n+                    reader_zebra_fx9600.limpiarROSpecsDuplicados();\r\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n"}, {"date": 1756931748369, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -250,9 +250,10 @@\n                 console.log('   zebraTest.consultar()  - Consulta ROSpecs existentes en el reader');\r\n                 console.log('   zebraTest.activar(id)  - Activa ROSpec existente (default: 1)');\r\n                 console.log('   zebraTest.simple()     - Muestra secuencia simple que funciona');\r\n                 console.log('   zebraTest.auto()       - Inicialización automática completa');\r\n-                console.log('   zebraTest.inteligente() - Inicialización inteligente (RECOMENDADO)');\r\n+                console.log('   zebraTest.inteligente() - Secuencia inteligente paso a paso');\r\n+                console.log('   zebraTest.limpiar()    - Limpia ROSpecs duplicados');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756933735205, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -192,8 +192,23 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            nuevo: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🆕 CREANDO ROSpec NUEVO optimizado para lectura continua');\r\n+\r\n+                    if (!reader_zebra_fx9600.connection) {\r\n+                        console.log('❌ No hay conexión activa. Ejecuta zebraTest.reconectar() primero');\r\n+                        return;\r\n+                    }\r\n+\r\n+                    // Crear ROSpec optimizado\r\n+                    reader_zebra_fx9600.crearROSpecOptimizado();\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756933796507, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -267,8 +267,9 @@\n                 console.log('   zebraTest.simple()     - Muestra secuencia simple que funciona');\r\n                 console.log('   zebraTest.auto()       - Inicialización automática completa');\r\n                 console.log('   zebraTest.inteligente() - Secuencia inteligente paso a paso');\r\n                 console.log('   zebraTest.limpiar()    - Limpia ROSpecs duplicados');\r\n+                console.log('   zebraTest.nuevo()      - Crea ROSpec optimizado (RECOMENDADO)');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756933814973, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -273,13 +273,14 @@\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n                 console.log('🎯 OPCIONES RECOMENDADAS:');\r\n-                console.log('   🧠 zebraTest.inteligente() - Maneja todo automáticamente');\r\n-                console.log('   📋 O secuencia manual:');\r\n+                console.log('   🆕 PARA LECTURA CONTINUA:');\r\n                 console.log('      1. zebraTest.reconectar()');\r\n-                console.log('      2. zebraTest.consultar()');\r\n-                console.log('      3. zebraTest.minima()');\r\n+                console.log('      2. zebraTest.nuevo()  ← Crea ROSpec optimizado 24/7');\r\n+                console.log('   🧹 PARA LIMPIAR DUPLICADOS:');\r\n+                console.log('      1. zebraTest.reconectar()');\r\n+                console.log('      2. zebraTest.limpiar()');\r\n             }\r\n         };\r\n \r\n         // Mostrar ayuda al cargar\r\n"}, {"date": 1756934282517, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -207,8 +207,28 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            forzarReal: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🔧 FORZANDO MODO REAL...');\r\n+\r\n+                    // Forzar configuración real\r\n+                    window.rfid_crono = 'real';\r\n+                    reader_zebra_fx9600.testMode = false;\r\n+\r\n+                    console.log('✅ Configuración forzada:');\r\n+                    console.log('   - window.rfid_crono:', window.rfid_crono);\r\n+                    console.log('   - reader_zebra_fx9600.testMode:', reader_zebra_fx9600.testMode);\r\n+\r\n+                    // Re-finalizar inicialización con modo real\r\n+                    reader_zebra_fx9600.finalizarInicializacion();\r\n+\r\n+                    console.log('🎉 Modo real forzado - Reader debería detectar tags ahora');\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756934302373, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -288,8 +288,9 @@\n                 console.log('   zebraTest.auto()       - Inicialización automática completa');\r\n                 console.log('   zebraTest.inteligente() - Secuencia inteligente paso a paso');\r\n                 console.log('   zebraTest.limpiar()    - Limpia ROSpecs duplicados');\r\n                 console.log('   zebraTest.nuevo()      - Crea ROSpec optimizado (RECOMENDADO)');\r\n+                console.log('   zebraTest.forzarReal() - Fuerza modo real si se queda en test');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756934824863, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -227,8 +227,36 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            secuencial: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('⏰ SECUENCIA AUTOMÁTICA CON DELAYS');\r\n+                    console.log('🔧 Configurando modo real...');\r\n+\r\n+                    // Configurar modo real\r\n+                    window.rfid_crono = 'real';\r\n+\r\n+                    console.log('1️⃣ Reconectando en 2 segundos...');\r\n+                    setTimeout(() => {\r\n+                        reader_zebra_fx9600.reconectar().then(() => {\r\n+                            console.log('2️⃣ Creando ROSpec en 3 segundos...');\r\n+                            setTimeout(() => {\r\n+                                reader_zebra_fx9600.crearROSpecOptimizado();\r\n+\r\n+                                console.log('3️⃣ Forzando modo real en 5 segundos...');\r\n+                                setTimeout(() => {\r\n+                                    this.forzarReal();\r\n+                                }, 5000);\r\n+                            }, 3000);\r\n+                        }).catch((error) => {\r\n+                            console.error('❌ Error en reconexión:', error);\r\n+                        });\r\n+                    }, 2000);\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             reconectar: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     reader_zebra_fx9600.reconectar();\r\n                 } else {\r\n"}, {"date": 1756934844283, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -317,8 +317,9 @@\n                 console.log('   zebraTest.inteligente() - Secuencia inteligente paso a paso');\r\n                 console.log('   zebraTest.limpiar()    - Limpia ROSpecs duplicados');\r\n                 console.log('   zebraTest.nuevo()      - Crea ROSpec optimizado (RECOMENDADO)');\r\n                 console.log('   zebraTest.forzarReal() - Fuerza modo real si se queda en test');\r\n+                console.log('   zebraTest.secuencial() - Secuencia automática con delays');\r\n                 console.log('   zebraTest.debug()      - Activa logs de debug (sin modo test)');\r\n                 console.log('   zebraTest.watchdog()   - Detecta bloqueos del hilo principal');\r\n                 console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n                 console.log('');\r\n"}, {"date": 1756937519660, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -262,8 +262,18 @@\n                 } else {\r\n                     console.log('❌ Reader Zebra FX9600 no disponible');\r\n                 }\r\n             },\r\n+            desconectar: function() {\r\n+                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n+                    console.log('🔌 DESCONECTANDO READER...');\r\n+                    reader_zebra_fx9600.desconectar().then(() => {\r\n+                        console.log('✅ Desconexión completada');\r\n+                    });\r\n+                } else {\r\n+                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n+                }\r\n+            },\r\n             debug: function() {\r\n                 if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                     console.log('🐛 ACTIVANDO LOGS DE DEBUG...');\r\n                     reader_zebra_fx9600.debugMode = true;\r\n"}, {"date": 1757159410620, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -198,15 +198,18 @@\n         {{^ extra }}\r\n         - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n         {{/ extra }}\r\n         - <span class=\"nombre\">{{ nombre }}</span><br>\r\n+<!--\r\n+\r\n         <span class=\"contador\">{{ idcrono }}</span>\r\n         - <span class=\"hora\">{{ hora }}</span>\r\n         - <span class=\"resultado\">{{ resultado }}</span>\r\n         <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n         <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n             <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n         </a>\r\n+        -->\r\n     </script>\r\n \r\n     <script type=\"x-tmpl-mustache\" id=\"tmpl-baja\">\r\n         <a href=\"#\" class=\"ui-icon-noicon ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n"}], "date": 1756849088673, "name": "Commit-0", "content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <title>Cronometraje Instantaneo</title>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\" >\r\n    <link rel=\"stylesheet\" href=\"css/jquery.mobile-1.4.5.min.css\" />\r\n    <link rel=\"stylesheet\" href=\"css/jquery.mobile-crono.css\" />\r\n    <link rel=\"stylesheet\" href=\"css/app.css\" />\r\n    <link rel=\"shortcut icon\" href=\"favicon.ico\">\r\n\r\n\r\n    <!-- Insert this line above script imports  -->\r\n    <script>if (typeof module === 'object') {window.module = module; module = undefined;}</script>\r\n\r\n    <!-- Scripts -->\r\n    <script type=\"text/javascript\" src=\"js/jquery-2.1.1.min.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/jquery.mobile-1.4.5.min.js\"></script>\r\n\r\n    <script type=\"text/javascript\" src=\"js/mustache.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/helpers.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/crono.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/app.js\"></script>\r\n\r\n    <script type=\"text/javascript\" src=\"js/hardware.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/invelion.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/chafon.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/reader1.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/reader2.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/reader3.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/chafon_810.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/reader_zebra_fx9600.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic1.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic2.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic3.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic4.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic5.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/cronopic6.js\"></script>\r\n    <script type=\"text/javascript\" src=\"js/hardwares/fotocelula1.js\"></script>\r\n\r\n    <script type=\"text/javascript\" src=\"cordova.js\"></script>\r\n\r\n    <script>\r\n        var enter = true;\r\n        app.initialize();\r\n        hardware.initialize();\r\n\r\n        // Funciones de ayuda para el modo test del Zebra FX9600\r\n        window.zebraTest = {\r\n            activar: function() {\r\n                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                    reader_zebra_fx9600.activarModoTest();\r\n                } else {\r\n                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n                }\r\n            },\r\n            desactivar: function() {\r\n                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                    reader_zebra_fx9600.desactivarModoTest();\r\n                } else {\r\n                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n                }\r\n            },\r\n            estado: function() {\r\n                if (typeof reader_zebra_fx9600 !== 'undefined') {\r\n                    console.log('🔍 Estado del reader Zebra FX9600:');\r\n                    console.log('   🧪 Modo test:', reader_zebra_fx9600.testMode);\r\n                    console.log('   ⏸️ Paso a paso:', reader_zebra_fx9600.stepByStep);\r\n                    console.log('   🔗 Conectado:', reader_zebra_fx9600.isConnected);\r\n                    console.log('   📡 Antena actual:', reader_zebra_fx9600.antena);\r\n                } else {\r\n                    console.log('❌ Reader Zebra FX9600 no disponible');\r\n                }\r\n            },\r\n            ayuda: function() {\r\n                console.log('🆘 AYUDA - Comandos disponibles para Zebra FX9600:');\r\n                console.log('   zebraTest.activar()    - Activa el modo test');\r\n                console.log('   zebraTest.desactivar() - Desactiva el modo test');\r\n                console.log('   zebraTest.estado()     - Muestra el estado actual');\r\n                console.log('   zebraTest.ayuda()      - Muestra esta ayuda');\r\n            }\r\n        };\r\n\r\n        // Mostrar ayuda al cargar\r\n        console.log('🧪 ZEBRA FX9600 TEST MODE DISPONIBLE');\r\n        console.log('💡 Escribe \"zebraTest.ayuda()\" en la consola para ver los comandos disponibles');\r\n\r\n    </script>\r\n    <!-- End Scripts -->\r\n\r\n</head>\r\n\r\n<body>\r\n\r\n    <div id=\"crono-btn\">\r\n        <a class=\"ui-btn\" href=\"#\" onclick=\"app.teclado('crono')\" data-icon=\"check\" style=\"color: #f7931e;\" id=\"crono\">\r\n            <img src=\"images/isotipo.png\">\r\n        </a>\r\n    </div>\r\n\r\n    <div data-role=\"footer\" data-position=\"fixed\" class=\"ui-footer ui-bar-a ui-footer-fixed slideup ui-body ui-body-b\">\r\n        <button onclick=\"app.teclado('config')\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-gear\">Config</button>\r\n        <button onclick=\"app.teclado('reenviar')\" id=\"reenviar-btn\" class=\"ui-btn ui-shadow ui-corner-all ui-btn-icon-left ui-icon-refresh\">Sincro&nbsp;(<span id=\"reenviar-cantidad\"></span>)</button>\r\n    </div>\r\n\r\n    <div data-role=\"page\" id=\"lecturas\">\r\n        <div data-role=\"content\">\r\n\r\n            <div class=\"ui-grid-a ui-corner-all\" style=\"font-size: 22px;\">\r\n              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"reloj\">00:00:00</div></div>\r\n              <div class=\"ui-block-a\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a text-center\" id=\"tiempo_pena\">-</div></div>\r\n              <div class=\"ui-block-b\" style=\"text-align: center;\"><div class=\"ui-body ui-bar ui-bar-a\" id=\"idparticipante\" style=\"color: #f7931e;\">-</div></div>\r\n              <div style=\"display: none;\" id=\"tagID\"></div>\r\n            </div>\r\n\r\n            <div id=\"penas\" style=\"text-align: center;\"></div>\r\n\r\n            <div id=\"crono-keys\">\r\n                <h1 style=\"color: white;\">CONTROL DE CHIPS</h1>\r\n                <p style=\"color: white;\">Acerca el chip al lector y comprueba tus datos</p>\r\n<!--\r\n\r\n                <div class=\"ui-grid-c ui-corner-all\">\r\n                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('buscador')\" data-icon=\"search\" id=\"buscador-btn\"></a></div>\r\n                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('1')\" alt=\"1\">1</a></div>\r\n                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('2')\" alt=\"2\">2</a></div>\r\n                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('3')\" alt=\"3\">3</a></div>\r\n                </div>\r\n                <div class=\"ui-grid-c ui-corner-all\">\r\n                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('scanqr')\" data-icon=\"camera\" id=\"qr\"></a></div>\r\n                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('4')\" alt=\"4\">4</a></div>\r\n                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('5')\" alt=\"5\">5</a></div>\r\n                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('6')\" alt=\"6\">6</a></div>\r\n                </div>\r\n                <div class=\"ui-grid-c ui-corner-all\">\r\n                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('esperar')\" data-icon=\"plus\" id=\"esperar\"></a></div>\r\n                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('7')\" alt=\"7\">7</a></div>\r\n                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('8')\" alt=\"8\">8</a></div>\r\n                    <div class=\"ui-block-d\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('9')\" alt=\"9\">9</a></div>\r\n                </div>\r\n                <div class=\"ui-grid-c ui-corner-all\">\r\n                    <div class=\"ui-block-a\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('multicrono')\" data-icon=\"check\" style=\"height: 25px;\" id=\"multicrono\"></a></div>\r\n                    <div class=\"ui-block-b\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('backspace')\" data-icon=\"arrow-l\" style=\"height: 25px;\"></a></div>\r\n                    <div class=\"ui-block-c\"><a data-role=\"button\" href=\"#\" onclick=\"app.teclado('0')\" alt=\"0\">0</a></div>\r\n                    <br>\r\n                </div>\r\n\r\n                <div id=\"pena\" class=\"ui-grid-a ui-corner-all\">\r\n                    <a data-role=\"button\" href=\"#\" onclick=\"app.teclado('pena')\" data-icon=\"check\" style=\"color: #f7931e;\">PENA</a>\r\n                    <br>\r\n                </div>\r\n\r\n                <div id=\"buscador\" class=\"ui-grid-a ui-corner-all\" style=\"text-align: center; display: none;\">\r\n                    <ul id=\"buscar\" data-role=\"listview\" data-inset=\"true\" data-filter=\"true\" data-filter-placeholder=\"\" data-filter-theme=\"a\"></ul>\r\n                    <div id=\"buscando-gif\" style=\"display: none;\"><img src=\"images/ajax-loader-2.gif\"></div>\r\n                    <br>\r\n                </div>\r\n\r\n            -->\r\n\r\n            </div>\r\n\r\n            <div id=\"totales\" class=\"ui-grid-a ui-corner-all\"></div>\r\n            <div id=\"consola\"></div>\r\n\r\n            <br/>\r\n\r\n            <ul data-role=\"listview\" class=\"ui-listview\" id=\"listaParticipantes\"></ul>\r\n\r\n            <br/><br/><br/><br/><br/><br/><br/>\r\n\r\n        </div>\r\n\r\n        <div data-role=\"popup\" id=\"popup\" data-theme=\"a\" class=\"ui-corner-all\"></div>\r\n        <a id=\"descargarLog\" style=\"display:none\"></a>\r\n\r\n    </div>\r\n\r\n    <!-- Start of second page -->\r\n    <div data-role=\"page\" id=\"kiosko\">\r\n        <iframe src=\"https://cronometrajeinstantaneo.com/resultados/vuelta-de-obligado-vob-etapa-3-circuito-owa-2324/ticket?idparticipante=153\" frameborder=\"0\" width=\"100%\" height=\"650px\"></iframe>\r\n    </div>\r\n\r\n\r\n    <!-- Templates -->\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-penas\">\r\n        {{# opciones_penas }}\r\n        <a data-role=\"button\" class=\"ui-link ui-btn ui-btn-inline ui-shadow ui-corner-all  ui-btn-b\" href=\"#\" onclick=\"app.opcionPena('{{ opcion_pena }}')\" alt=\"{{ opcion_pena }}\">{{ opcion_pena }}</a>\r\n        {{/ opciones_penas }}\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-li\">\r\n        <li class=\"ui-li ui-li-static ui-body-c\" id=\"{{ idcrono }}\">{{ mensaje }}</li>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-totales\">\r\n        <div class=\"ui-body ui-bar ui-bar-a text-center\">\r\n            Bar: {{ barrido }} | Tot: {{ total }} | Dif: {{ diff }} | Últ: {{ ultimo }}\r\n            {{# antenas }}\r\n            <br>\r\n            1: {{ antena_1 }} | 2: {{ antena_2 }} | 3: {{ antena_3 }} | 4: {{ antena_4 }} |\r\n            5: {{ antena_5 }} | 6: {{ antena_6 }} | 7: {{ antena_7 }} | 8: {{ antena_8 }}\r\n            {{/ antenas }}\r\n        </div>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-control\">\r\n        <span class=\"evento\">{{ evento }}</span><br>\r\n        <span class=\"codigo\"><b>{{ codigo }}</b></span>\r\n        - <span class=\"nombre\">{{ nombre }}</span>\r\n        <span class=\"tipo\">({{ tipo }})</span>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-cargando\">\r\n        <a href=\"#\" onclick=\"app.cancel($(this))\" class=\"ui-icon-delete ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n        </a>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"hora\">{{ hora }}</span>\r\n        - <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-lectura\">\r\n        {{^ extra }}\r\n        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"ui-icon-edit ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n        </a>\r\n        {{/ extra }}\r\n        {{# extra }}\r\n        <a href=\"#\" class=\"ui-icon-check ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n        </a>\r\n        {{/ extra }}\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n        {{# extra }}\r\n        - <span class=\"extra\">{{ extra }}</span>\r\n        {{/ extra }}\r\n        {{^ extra }}\r\n        {{/ extra }}\r\n        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-baja\">\r\n        <a href=\"#\" class=\"ui-icon-noicon ui-btn-icon-left\" id=\"{{ idlectura }}\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n            <span class=\"idlectura\" style=\"display: none;\">{{ idlectura }}</span>\r\n            <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n        </a>\r\n        <del>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n        - <span class=\"tiempo_listo\">{{ tiempo_listo }}</span>\r\n        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"hora\">{{ hora }}</span>\r\n        </del>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-error\">\r\n        <a href=\"#\" onclick=\"app.mod($(this))\" class=\"re-enviar ui-icon-edit ui-btn-icon-left\" id=\"{{ idcrono }}\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n        </a>\r\n        <span class=\"tiempo\" style=\"display: none;\">{{ tiempo }}</span>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span><br>\r\n        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"hora\">{{ hora }}</span>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-default\">\r\n        <span class=\"json\">ERROR: {{ json }}</span>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-esperando\">\r\n        <a href=\"#\" onclick=\"app.esperando($(this))\" class=\"esperando ui-icon-check ui-btn-icon-left\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n        </a>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n        <span class=\"tagID\" style=\"display: none;\">{{ tagID }}</span>\r\n        <span class=\"tiempo\"> </span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-pena\">\r\n        <a href=\"#\" onclick=\"\" class=\"ui-icon-delee ui-btn-icon-left\" id=\"{{ idpena }}\">\r\n            <span class=\"idcrono\" style=\"display: none;\">{{ idcrono }}</span>\r\n            <span class=\"idcambiotiempo\" style=\"display: none;\">{{ idcambiotiempo }}</span>\r\n        </a>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"tiempo\">{{ tiempo }} seg</span>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-tag\">\r\n        <a href=\"#\" onclick=\"\" class=\"ui-icon-check ui-btn-icon-left\">\r\n        </a>\r\n        <span class=\"idparticipante\">{{ idparticipante }}</span>\r\n        - <span class=\"nombre\">{{ nombre }}</span><br>\r\n        <span class=\"contador\">{{ idcrono }}</span>\r\n        - <span class=\"resultado\">{{ resultado }}</span>\r\n        <span class=\"accion\">{{ accion }} {{ tagID }}</span>\r\n        <a href=\"#\" onclick=\"app.star($(this))\" class=\"ui-icon-nostar ui-btn-icon-right\">\r\n            <span class=\"resaltar\" style=\"display: none;\">{{ resaltar }}</span>\r\n        </a>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-edit\">\r\n    <div id=\"editForm\">\r\n        <div style=\"padding:10px 20px; text-align: center;\">\r\n            <input type=\"hidden\" name=\"idcrono\" value=\"{{ idcrono }}\" />\r\n            <input type=\"hidden\" name=\"idlectura\" value=\"{{ idlectura }}\" />\r\n            <input type=\"hidden\" name=\"resultado\" value=\"{{ resultado }}\" />\r\n            <input type=\"hidden\" name=\"hora\" value=\"{{ hora }}\" />\r\n            <input type=\"hidden\" name=\"tiempo\" value=\"{{ tiempo }}\" />\r\n            <input type=\"hidden\" name=\"tiempo_listo_original\" value=\"{{ tiempo_listo }}\" />\r\n\r\n            <label>Participante:<br />\r\n                <input name=\"idparticipante\" type=\"number\" pattern=\"[0-9]*\" inputmode=\"numeric\" value=\"{{ idparticipante }}\" style=\"width:100px;\" onClick=\"this.select();\" />\r\n            </label>\r\n\r\n            <label>Hora:<br />\r\n                <input name=\"hora_1\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_1 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n                <b>:</b>\r\n                <input name=\"hora_2\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_2 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n                <b>:</b>\r\n                <input name=\"hora_3\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{2}\" onchange=\"app.changeHora()\" value=\"{{ hora_3 }}\" inputmode=\"numeric\" style=\"width:50px;\"  onClick=\"this.select();\" />\r\n                <b>.</b>\r\n                <input name=\"hora_4\" onkeyup=\"app.keyupHora()\" type=\"number\" pattern=\"[0-9]{3}\" onchange=\"app.changeHora()\" value=\"{{ hora_4 }}\" inputmode=\"numeric\" style=\"width:70px;\"  onClick=\"this.select();\" />\r\n            </label>\r\n\r\n            {{ #idlectura }}\r\n            <label>Tiempo:<br />\r\n                <input type=\"text\" name=\"tiempo_listo\" onchange=\"app.changeTiempo()\" value=\"{{ tiempo_listo }}\" disabled />\r\n            </label>\r\n            {{ /idlectura }}\r\n            {{ ^idlectura }}\r\n                <input type=\"hidden\" name=\"tiempo_listo\" value=\"{{ tiempo_listo }}\" />\r\n            {{ /idlectura }}\r\n\r\n            <a onclick=\"app.menos();\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-d ui-btn-icon-notext ui-btn-inline\"></a>\r\n            <a onclick=\"app.mas()\" href=\"#\" class=\"ui-btn ui-btn-icon-left ui-icon-arrow-u ui-btn-icon-notext ui-btn-inline\"></a><br />\r\n\r\n            <button type=\"submit\" onclick=\"app.ok()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Modificar</button>\r\n            <button type=\"submit\" onclick=\"app.baja()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Borrar</button>\r\n        </div>\r\n    </div>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-configurar\">\r\n    <div id=\"configurarForm\">\r\n        <div style=\"padding:10px 20px; text-align: center;\">\r\n            <label>Código del Control:<br />\r\n                <input type=\"text\" name=\"codigo\" value=\"{{ codigo }}\" maxlength=\"4\" />\r\n            </label>\r\n            <label>Cronometrador:<br />\r\n                <input type=\"text\" name=\"cronometrador\" value=\"{{ cronometrador }}\" maxlength=\"40\" />\r\n            </label>\r\n\r\n            {{ #compatible_nube_hibrida }}\r\n            <label>Nube híbrida:<br />\r\n                <input type=\"checkbox\" name=\"nube_hibrida\" {{ nube_hibrida }}/>\r\n            </label>\r\n            {{ /compatible_nube_hibrida }}\r\n\r\n            <button type=\"submit\" onclick=\"app.configurar()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Configurar</button>\r\n\r\n            <hr>\r\n\r\n            {{ #compatible_hardware }}\r\n            <label>Hardware configurado:<br />\r\n                {{ conectar_hardware }}\r\n            </label>\r\n\r\n            <button type=\"submit\" onclick=\"app.configHardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-check ui-btn-inline\">Hardware</button>\r\n\r\n            {{ /compatible_hardware }}\r\n\r\n            <p style=\"font-size: small\">v {{ version }}</p>\r\n        </div>\r\n    </div>\r\n    </script>\r\n\r\n    <script type=\"x-tmpl-mustache\" id=\"tmpl-hardware\">\r\n    <div id=\"hardwareForm\">\r\n        <div style=\"padding:10px 20px; text-align: center;\">\r\n\r\n            <h4>Dispositivo 1</h4>\r\n            <select name=\"dispositivo1\">\r\n            {{ #android }}\r\n                <option value=\"\">No conectado</option>\r\n                <option value=\"cronopic4\">Fotocélula Bluetooth</option>\r\n                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n            {{ /android }}\r\n            {{ #windows }}\r\n                <option value=\"\">No conectado</option>\r\n                <option value=\"reader1\">Reader UHF RFID por USB</option>\r\n                <option value=\"reader2\">Reader UHF RFID por LAN</option>\r\n                <option value=\"reader3\">USB Desktop UHF RFID</option>\r\n                <option value=\"chafon_810\">Reader Chafon 810 por LAN</option>\r\n                <option value=\"reader_zebra_fx9600\">Zebra FX9600 RFID Reader (LLRP)</option>\r\n                <option value=\"cronopic4\">Fotocélula Cronopic Bluetooth</option>\r\n                <option value=\"cronopic3\">Fotocélula Cronopic Wifi Multicanal</option>\r\n                <option value=\"cronopic1\">Fotocélula Cronopic Wifi CH1</option>\r\n                <option value=\"cronopic2\">Fotocélula Cronopic Wifi CH2</option>\r\n                <option value=\"cronopic5\">Fotocélula Cronopic con Reloj Interno</option>\r\n                <option value=\"cronopic6\">Fotocélula Bluetooth Low Energy</option>\r\n                <option value=\"fotocelula1\">Fotocélula Genérica por Espacio</option>\r\n            {{ /windows }}\r\n            </select>\r\n            <br />\r\n            {{ #windows }}\r\n            <select name=\"puerto1\">\r\n                <option value=\"\">No utilizar COM</option>\r\n                <option value=\"COM1\">COM1</option>\r\n                <option value=\"COM2\">COM2</option>\r\n                <option value=\"COM3\">COM3</option>\r\n                <option value=\"COM4\">COM4</option>\r\n                <option value=\"COM5\">COM5</option>\r\n                <option value=\"COM6\">COM6</option>\r\n                <option value=\"COM7\">COM7</option>\r\n                <option value=\"COM8\">COM8</option>\r\n                <option value=\"COM9\">COM9</option>\r\n                <option value=\"COM10\">COM10</option>\r\n                <option value=\"COM11\">COM11</option>\r\n                <option value=\"COM12\">COM12</option>\r\n                <option value=\"COM13\">COM13</option>\r\n                <option value=\"COM14\">COM14</option>\r\n                <option value=\"COM15\">COM15</option>\r\n                <option value=\"COM16\">COM16</option>\r\n                <option value=\"COM17\">COM17</option>\r\n                <option value=\"COM18\">COM18</option>\r\n                <option value=\"COM19\">COM19</option>\r\n                <option value=\"COM20\">COM20</option>\r\n            </select>\r\n            <br />\r\n            {{ /windows }}\r\n            {{ #linux  }}\r\n            <select name=\"puerto1\">\r\n                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n            </select>\r\n            <br />\r\n            {{ /linux  }}\r\n            </select>\r\n            <br />\r\n            {{ #windows }}\r\n            <h4>Configuración de reader</h4>\r\n\r\n            <label>Modo:\r\n            <select name=\"rfid_crono\" onchange=\"isTagCodigo()\">\r\n                <option value=\"test\">Pruebas</option>\r\n                <option value=\"real\">Cronometraje Real Time</option>\r\n                <option value=\"fast\">Cronometraje Fast Switch</option>\r\n                <option value=\"tag\">Asignar chip</option>\r\n                <option value=\"tag_codigo\">Grabar chip</option>\r\n            </select>\r\n            </label>\r\n\r\n            <div id=\"tag_codigo\" style=\"display: none\">\r\n                <label>Prefijo <input type=\"text\" name=\"tag_prefijo\" maxlength=\"8\" style=\"width: 100px;\" /></label>\r\n            </div>\r\n\r\n            <label>Ignorar repetidos:\r\n            <select name=\"rebote\">\r\n                <option value=\"0\">Nunca</option>\r\n                <option value=\"5000\">5 seg</option>\r\n                <option value=\"30000\">30 seg</option>\r\n                <option value=\"300000\">5 min</option>\r\n                <option value=\"1800000\">30 min</option>\r\n                <option value=\"86400000\">24 hs</option>\r\n            </select>\r\n            </label>\r\n\r\n            <!--\r\n            <label>Intervalo de lectura:\r\n            <select name=\"intervalo\">\r\n                <option value=\"25\">25 ms</option>\r\n                <option value=\"50\">50 ms</option>\r\n                <option value=\"100\">100 ms</option>\r\n                <option value=\"250\">250 ms</option>\r\n                <option value=\"500\">500 ms</option>\r\n                <option value=\"1000\">1 seg</option>\r\n            </select>\r\n            </label>\r\n            -->\r\n\r\n            <label>Reproducir sonido: <input type=\"checkbox\" name=\"sonido\"/></label>\r\n\r\n            <h4>Antenas conectadas:</h4>\r\n            <label style=\"display: inline;\">1 <input type=\"checkbox\" name=\"antena_1\"/></label>\r\n            <label style=\"display: inline;\">2 <input type=\"checkbox\" name=\"antena_2\"/></label>\r\n            <label style=\"display: inline;\">3 <input type=\"checkbox\" name=\"antena_3\"/></label>\r\n            <label style=\"display: inline;\">4 <input type=\"checkbox\" name=\"antena_4\"/></label>\r\n            <br />\r\n            <label style=\"display: inline;\">5 <input type=\"checkbox\" name=\"antena_5\"/></label>\r\n            <label style=\"display: inline;\">6 <input type=\"checkbox\" name=\"antena_6\"/></label>\r\n            <label style=\"display: inline;\">7 <input type=\"checkbox\" name=\"antena_7\"/></label>\r\n            <label style=\"display: inline;\">8 <input type=\"checkbox\" name=\"antena_8\"/></label>\r\n\r\n            <br />\r\n            {{ /windows }}\r\n            {{ #linux  }}\r\n            <select name=\"puerto2\">\r\n                <option value=\"/dev/ttyUSB0\">/dev/ttyUSB0</option>\r\n                <option value=\"/dev/ttyUSB1\">/dev/ttyUSB1</option>\r\n                <option value=\"/dev/ttyUSB2\">/dev/ttyUSB2</option>\r\n                <option value=\"/dev/ttyUSB3\">/dev/ttyUSB3</option>\r\n            </select>\r\n            <br />\r\n            {{ /linux  }}\r\n\r\n            <br />\r\n\r\n            <button type=\"submit\" onclick=\"app.hardware()\" class=\"ui-btn ui-btn-icon-left ui-icon-delete ui-btn-inline\">Configurar</button>\r\n        </div>\r\n    </div>\r\n    </script>\r\n    <!-- End Templates -->\r\n\r\n</body>\r\n</html>\r\n"}]}