// CONST
const PLATFORM = 'windows'; // android, ios, windows, linux
const VERSION = "2.16.0";
const URL_API_ONLINE = "https://cronometrajeinstantaneo.com/api/";
// const URL_API_ONLINE = "http://cronometrajeinstantaneo.des/api/";
const URL_API_LOCAL = "http://cronometrajeinstantaneo.lan/api/";
const URL_API_ADMIN = "https://admin.cronometrajeinstantaneo.com/api/";
const CODIGO_LENGHT = 4;
const HASH_SALT = 'DQ9IdeIDrN4FwA8ZeYtAJRFV8IuKeRo0';
const DEFAULTS = {
    'url_api': "https://cronometrajeinstantaneo.com/api/",
    'codigo': "DEMO",
    'cronometrador': "Cronometrador",
    'nube_hibrida': false,
    'conectar_hardware': false,
    'evento': 'Evento de prueba',
    'nombre': 'Control de prueba',
    'tipo': 'final',
    'limitar_penas': '',
    'hardwares': []
};

// Fuerzo borrar cache si viene desde la versión 3.x
if (getConfig('url_api') != URL_API_ONLINE && !getConfig('nube_hibrida')) {
    localStorage.clear();
}

// VAR
var url_api = getConfig('url_api');
var nube_hibrida = getConfig('nube_hibrida');
var conectar_hardware = getConfig('conectar_hardware');
var codigo = getConfig('codigo');
var cronometrador = getConfig('cronometrador');
var idcrono = getIdCrono();
var tipo = getConfig('tipo');
var limitar_penas = getConfig('limitar_penas');
var hardwares = getConfig('hardwares');
var requests = [];
var request_buscando = false;
var request_busqueda;
var bep = new Audio('bep.mp3');

var rfid_crono = 'test';
var sonido = true;
var rebote = 30000;
var intervalo = 50;
var tag_prefijo = '';
var antena_1 = true;
var antena_2 = true;
var antena_3 = true;
var antena_4 = true;
var antena_5 = false;
var antena_6 = false;
var antena_7 = false;
var antena_8 = false;
var firmware = '';
var temperature = '';
