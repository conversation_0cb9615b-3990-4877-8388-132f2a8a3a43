{"sourceFile": "app/www/js/hardware.js", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1757113468835, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1757113468835, "name": "Commit-0", "content": "\r\nvar hardware = {\r\n\r\n    conexiones: [],\r\n\r\n    initialize: function () {\r\n        this.desconectar();\r\n//        document.addEventListener(\"deviceready\", hardware.onDeviceReady, false);\r\n//        document.addEventListener(\"DOMContentLoaded\", hardware.onDeviceReady, false);\r\n    },\r\n\r\n    onDeviceReady: function () {\r\n        try {\r\n            hardwares.forEach(function(element) {\r\n                switch (element.dispositivo) {\r\n                    case 'reader1': var conexion = reader1; break;\r\n                    case 'reader2': var conexion = reader2; break;\r\n                    case 'reader3': var conexion = reader3; break;\r\n                    case 'chafon_810': var conexion = chafon_810; break;\r\n                    case 'reader_zebra_fx9600': var conexion = reader_zebra_fx9600; break;\r\n                    case 'cronopic1': var conexion = cronopic1; break;\r\n                    case 'cronopic2': var conexion = cronopic2; break;\r\n                    case 'cronopic3': var conexion = cronopic3; break;\r\n                    case 'cronopic4': var conexion = cronopic4; break;\r\n                    case 'cronopic5': var conexion = cronopic5; break;\r\n                    case 'cronopic6': var conexion = cronopic6; break;\r\n                    case 'fotocelula1': var conexion = fotocelula1; break;\r\n                }\r\n                conexion.config.puerto = element.puerto;\r\n                hardware.conexiones.push(conexion);\r\n            })\r\n            delay(hardware.conectar(), 2000);\r\n        } catch (e) {\r\n            hardware.desconectar();\r\n            alerta('Hardware no conectado');\r\n            console.log('Hardware no conectado: ', e.message);\r\n        }\r\n    },\r\n\r\n    configurar: function () {\r\n\r\n        var hardwareForm = $('#hardwareForm');\r\n        for (i = 1; i <= 1; i++) { // Permito una sola conexión por ahora\r\n            var dispositivo = hardwareForm.find(\"select[name=dispositivo\"+i+\"] option:selected\").val();\r\n            var puerto = hardwareForm.find(\"select[name=puerto\"+i+\"] option:selected\").val();\r\n\r\n            if (PLATFORM == 'windows') {\r\n                window.rfid_crono = hardwareForm.find(\"select[name=rfid_crono] option:selected\").val();\r\n                window.sonido = hardwareForm.find(\"input[name=sonido]\").is(':checked');\r\n                // window.intervalo = hardwareForm.find(\"select[name=intervalo] option:selected\").val();\r\n                window.rebote = hardwareForm.find(\"select[name=rebote] option:selected\").val();\r\n                window.tag_prefijo = hardwareForm.find(\"input[name=tag_prefijo]\").val();\r\n                window.antena_1 = hardwareForm.find(\"input[name=antena_1]\").is(':checked');\r\n                window.antena_2 = hardwareForm.find(\"input[name=antena_2]\").is(':checked');\r\n                window.antena_3 = hardwareForm.find(\"input[name=antena_3]\").is(':checked');\r\n                window.antena_4 = hardwareForm.find(\"input[name=antena_4]\").is(':checked');\r\n                window.antena_5 = hardwareForm.find(\"input[name=antena_5]\").is(':checked');\r\n                window.antena_6 = hardwareForm.find(\"input[name=antena_6]\").is(':checked');\r\n                window.antena_7 = hardwareForm.find(\"input[name=antena_7]\").is(':checked');\r\n                window.antena_8 = hardwareForm.find(\"input[name=antena_8]\").is(':checked');\r\n                // If all antena_* is false, set antenna_1 to true\r\n                if (!window.antena_1 && !window.antena_2 && !window.antena_3 && !window.antena_4 && !window.antena_5 && !window.antena_6 && !window.antena_7 && !window.antena_8) {\r\n                    window.antena_1 = true;\r\n                }\r\n            }\r\n        }\r\n//        if (hardwares.length < 1 || hardwares[0].dispositivo != dispositivo)\r\n//        hardware.desconectar();\r\n//        hardwares = [];\r\n\r\n        if (dispositivo) {\r\n            hardwares.push({\"dispositivo\": dispositivo, \"puerto\": puerto});\r\n        }\r\n        setConfig('hardwares', hardwares);\r\n        hardware.onDeviceReady();\r\n\r\n    },\r\n\r\n    desconectar: function () {\r\n        try {\r\n            for (i = 0; i < hardware.conexiones.length; i++) {\r\n                hardware.conexiones[i].desconectar();\r\n            }\r\n        } catch (err) {\r\n            alerta('Desconectando hardware');\r\n            // console.log('Desconectando hardware: ', err);\r\n        }\r\n        hardware.conexiones = [];\r\n        hardwares = [];\r\n        setConfig('hardwares', hardwares);\r\n    },\r\n\r\n    conectar: function () {\r\n        for (i = 0; i < hardware.conexiones.length; i++) {\r\n            hardware.conexiones[i].conectar();\r\n        }\r\n    }\r\n\r\n}\r\n"}]}