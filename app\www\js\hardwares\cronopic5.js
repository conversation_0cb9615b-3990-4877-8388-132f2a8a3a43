
var cronopic5 = {

    'config': {
        'enter': true,
        'baudios': 9600,
        'puerto': false
    },

    'serialport': null,

    conectar: function () {
        var that = this;

        var SerialPortClass = require("serialport");
        this.serialport = new SerialPortClass(this.config.puerto, {baudRate: this.config.baudios});

        this.serialport.on('open', function () {
            console.log('CronoPic conectado en el puerto ' + that.config.puerto);
        });
        this.serialport.write(0x05);
        this.serialport.on('data', function(data) {
            that.recibir(data);
        });

    },

    desconectar: function () {
        var that = this;
        this.serialport.close(function (err) {
            console.log('CronoPic desconectado del puerto ' + that.config.puerto, err);
        });
    },

    recibir: function (data) {
       // console.log(data)
        if (!this.validar(data))
            return false;

        // Entra algo como <11:22:33.44>
        var data_string = data.toString();
        let hora = armar_hora(
            data_string.substring(1,3),
            data_string.substring(4,6),
            data_string.substring(7,9),
            data_string.substring(10,12))
        let date = getDate(hora);

        var lectura = $(".crono-esperando").last().parent();
        if (this.enter && lectura.length)
            app.esperando(lectura, date);

        else
            app.crono(null, date);
    },

    validar: function (data) {
        if (data[0] == 60 && data[3] == 58) // < signo mayor y : dos puntos
            return true;
        else {
            alerta(data);
            alerta(data.toString());
            return false;
        }
   }

}
