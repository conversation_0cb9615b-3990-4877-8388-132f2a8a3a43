#!/usr/bin/env node

console.log('🚀 Zebra Debug Tester - Iniciando...');

// Test básico de carga
try {
    console.log('📦 Cargando módulos...');
    const fs = require('fs');
    const path = require('path');
    console.log('✅ Módulos básicos cargados');
    
    console.log('📦 Cargando llrpjs...');
    const llrpjs = require('llrpjs');
    console.log('✅ llrpjs cargado correctamente');
    
    console.log('📋 Verificando archivos de configuración...');
    
    // Verificar config
    if (fs.existsSync('zebra-config.json')) {
        const config = JSON.parse(fs.readFileSync('zebra-config.json', 'utf8'));
        console.log('✅ zebra-config.json encontrado:', config);
    } else {
        console.log('⚠️ zebra-config.json no encontrado');
    }
    
    // Verificar rospec
    if (fs.existsSync('zebra-rospec.json')) {
        const rospec = JSON.parse(fs.readFileSync('zebra-rospec.json', 'utf8'));
        console.log('✅ zebra-rospec.json encontrado');
        console.log('   ROSpec ID:', rospec.data.ROSpec.ROSpecID);
        console.log('   Antenas:', rospec.data.ROSpec.AISpec.AntennaIDs);
    } else {
        console.log('⚠️ zebra-rospec.json no encontrado');
    }
    
    console.log('\n🎯 Test de conexión básica...');
    
    // Test de conexión simple
    const LLRPClient = llrpjs.LLRPClient;
    const client = new LLRPClient({
        host: '*************',
        port: 5084
    });
    
    console.log('🔗 Intentando conectar...');
    
    client.on('connect', function() {
        console.log('✅ CONECTADO al reader');
        setTimeout(() => {
            console.log('🔌 Desconectando...');
            client.disconnect();
        }, 2000);
    });
    
    client.on('error', function(error) {
        console.log('❌ ERROR:', error.message);
        process.exit(1);
    });
    
    client.on('disconnect', function() {
        console.log('✅ Desconectado correctamente');
        process.exit(0);
    });
    
    // Timeout de seguridad
    setTimeout(() => {
        console.log('⏰ Timeout - No se pudo conectar en 10 segundos');
        process.exit(1);
    }, 10000);
    
    client.connect();
    
} catch (error) {
    console.error('❌ Error fatal:', error.message);
    console.error(error.stack);
    process.exit(1);
}
