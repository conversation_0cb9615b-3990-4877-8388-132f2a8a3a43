
var reader3 = {

    'nombre': 'USB Desktop UHF RFID',
    'config': {
        'enter': true,
        'baudios': 115200,
        'puerto': 0,
        'interval': 25
    },

    'connection': null,
    'interval': null,
    'antena': 0,
    'tagsRecientes': [],
    'ultimoTag': '',
    'tiempoReciente': 10000, // En milisegundos

    totales: {
        barrido: 1,
        total: 0,
        diff: 0,
        ultimo: 0,
        antenas: [0,0,0,0,0,0,0,0,0],
    },

    conectar: function () {
        var that = this;
        this.tagsRecientes = [];
        this.antena = 0;
        this.cambio = true;
        this.continuar = false;
        this.totales = {
            barrido: 0,
            total: 0,
            diff: 0,
            ultimo: 0,
            antenas: [0,0,0,0,0,0,0,0,0],
        };

        const { SerialPort } = require('serialport');
        this.connection = new SerialPort({path: this.config.puerto, baudRate: this.config.baudios, encoding: 'hex'});

        this.connection.on('error', function(error) {
            hardware.desconectar();
            if (typeof error.message != 'undefined'
                || error.message.indexOf('Unknown error code 31')) // Puerto colgado
                messageToUser = 'El puerto ' + that.config.puerto + ' no responde.\nRevise la correcta configuración, intente re-conectar el dispositivo y reiniciar la app.\nSi no funciona contacte nuestro soporte técnico.\n';
            else
                messageToUser = 'No hay conexión con el reader\nReporte este mensaje técnico interno al soporte: ' + error;
            alert(messageToUser);
        });

        this.connection.on('open', function () {

            alerta(that.nombre + ' conectado en el puerto ' + that.config.puerto);
            // Podemos hacer un control de temperatura y cuelgue cada 5 segundos
            // that.interval = setInterval(function () {that.consultar()}, 1000);

            // $("#consola").show();
            that.consultar('firmware');
        });

        this.connection.on('data', function(data) {
            that.recibir(data);
        });

    },

    desconectar: function () {
        var that = this;
        // clearInterval(this.interval);
        if (typeof this.connection != 'undefined') {
            this.connection.close(function (err) {
                alerta(that.nombre + ' desconectado del puerto ' + that.config.puerto);
                console.log(that.nombre + ' desconectado del puerto ' + that.config.puerto, err);
            });
        }
    },

    consultar: function (command) {

        let hex;
        if (command == 'firmware') {
            hex = invelion.hexEncode({ cmd: command });

        } else if (['test', 'real', 'fast', 'tag', 'tag_codigo'].includes(command)) {
            hex = invelion.hexEncode({ cmd: 'real' });

        } else {
            alertaConsola('Command ' + command + ' not found');
        }

        var buffer = Buffer.from(hex, 'hex');

        this.connection.write(buffer, function(err) {
            alertaConsola('S' + hex);
            if (err)
                alertaConsola('S' + hex + ' error ', err.message);
        });

    },

    recibir: function (data) {

        var that = this;
        dataHex = data.toString('hex').toUpperCase();
        alertaConsola('R' + dataHex );

        invelion.splitHex(dataHex).forEach( function(hex, i, array) {

            let dataJSON = invelion.hexDecode(hex);
            // console.log(JSON.stringify(dataJSON));

            if (dataJSON.error && dataJSON.error != 'antena-missing') {
                alerta('Error: ' + dataJSON.error + ' | Code: ' + dataJSON.errorCode);
            }

            if (dataJSON.cmd == 'firmware') {
                alerta('Firmware: ' + dataJSON.firmware);

                this.antena = 0;
                that.consultar('real');

            } else if (dataJSON.cmd == 'epc') {
                that.epc(dataJSON.epc, dataJSON.antena);

            } else if (dataJSON.cmd == 'real') {
                that.consultar('real');

            }

        });

        return;
    },

    epc: function (tagID, antena = false) {
        if (window.sonido)
            bep.play();
        alertaConsola('EPC ' + this.antena + ' ' + tagID);
        this.totales.ultimo++;
        this.totales.total++;
        if (!antena || window.rfid_crono != 'fast')
            antena = this.antena;
        this.totales.antenas[antena] = !isNaN(this.totales.antenas[antena])
            ? this.totales.antenas[antena] + 1
            : 1;
        if (!this.tagReciente(tagID)) {
            $("#tagID").html(tagID);
            let idparticipante = $("#idparticipante").html();
            if (!isNaN(idparticipante)
                && (window.rfid_crono == 'tag' || window.rfid_crono == 'tag_codigo'))
                app.tag(window.rfid_crono);

            else if (window.rfid_crono == 'real' || window.rfid_crono == 'fast' || window.rfid_crono == 'tag')
                app.crono();
        }

    },

    tagReciente: function (tagID) {

        var time = new Date().getTime();

        for (i = 0; i < this.tagsRecientes.length; i++) {
            if (this.tagsRecientes[i].tagID == tagID) {
                if (this.tagsRecientes[i].time > (time - window.rebote)) {
                    return true;
                } else {
                    this.tagsRecientes[i].time = time;
                    return false;
                }
            }
        }

        let reciente = {'time': time, 'tagID': tagID};
        this.tagsRecientes.push(reciente);
        return false;

    },

    nextAntena: function () {
        return 1;
    }
}
