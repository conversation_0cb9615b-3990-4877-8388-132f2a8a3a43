function getDate(hora = null) {
    let date = new Date();
    let dia = date.getFullYear()
        + '-' + ("0" + (date.getMonth() + 1)).slice(-2)
        + '-' + ("0" + date.getDate()).slice(-2);
    if (!hora)
        hora = armar_hora(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());

    return {
        "dia": dia,
        "hora": hora,
        "tiempo": (dia + ' ' + hora)
    }
}

function armar_hora(horas, minutos, segundos, milisegundos) {
    var hora = ((horas < 10) ? "0" : "") + horas + ":"
        + ((minutos < 10) ? "0" : "") + minutos + ":"
        + ((segundos < 10) ? "0" : "") + segundos;

    if (milisegundos !== false)
        hora = hora + "."
            + ((milisegundos < 100) ? "0" : "") + ((milisegundos < 10) ? "0" : "") + milisegundos;

    return hora;
}

function reloj() {
    var date = new Date();
    var hora = armar_hora(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    $("#reloj").html(hora);
}

function alerta(mensaje) {
    $("#listaParticipantes").prepend(renderTemplate('li', {mensaje: mensaje}));
}

function alertaFija(mensaje) {
    $("#listaParticipantes li").first().replaceWith(renderTemplate('li', {mensaje: mensaje}));
}

function alertaConsola(mensaje) {
    if ($("#consola").is(":visible")) {
        $("#consola").append(mensaje + "<br>");
        $("#consola").html($("#consola").html().split("<br>").slice(-500).join("<br>"));
        // console.info(mensaje);
    }
}

function isJson(item) {
    item = typeof item !== "string"
        ? JSON.stringify(item)
        : item;

    try {
        item = JSON.parse(item);
    } catch (e) {
        return false;
    }

    if (typeof item === "object" && item !== null) {
        return true;
    }

    return false;
}

function validarCodigo(codigo) {
    return (typeof codigo != "string" || codigo.length != CODIGO_LENGHT)
        ? false
        : true;
}

function descargarLog() {
    var cronos = findLocalItems(codigo);
    var dataStr = "";
    for (i in cronos) {
        dataStr+= (JSON.stringify(cronos[i]));
    }
    alert(dataStr);

    // var descargarLog = $("#descargarLog");
    // descargarLog.attr("href", dataStr);
    // descargarLog.attr("download", codigo + "-" + device.uuid + ".json");
    // $("#descargarLog").trigger('click');
    // $("#descargarLog").trigger('touchstart');

}

function test(lecturas) {
    for (i = 0; i < lecturas; i++) {
        $("#idparticipante").html(i);
        $("#crono").click();
    }
}

function urlDes() {
    url_api = 'http://cronometrajeinstantaneo.des/api/';
}

function validarCodigo(codigo) {
    return (typeof codigo != "string" || codigo.length != CODIGO_LENGHT)
        ? false
        : true;
}

function getIdCrono() {

    if (localStorage.getItem(codigo + "-idcrono") !== null) {
        return localStorage.getItem(codigo + "-idcrono");

    } else {
        var idcrono = 1;
        localStorage.setItem(codigo + "-idcrono", idcrono);
        return idcrono;
    }

}

function plusIdCrono() {

    window.idcrono++;
    localStorage.setItem(codigo + "-idcrono", window.idcrono);

}

function getConfig(item) {

    if (localStorage.getItem(item) !== null) {
        var value = JSON.parse(localStorage.getItem(item));

    } else {
        var value = DEFAULTS[item];
        localStorage.setItem(item, JSON.stringify(value));
    }
    return value;

}

function setConfig(item, value) {
    window[item] = value;
    localStorage.setItem(item, JSON.stringify(value));
}

function findLocalItems(query) {
    var i, keys = [];
    for (i in localStorage) {
        if (localStorage.hasOwnProperty(i) && i.match(query) && i != query + '-idcrono')
            keys.push(i)
    }
    keys.sort();

    var cronos = [];
    for (i in keys) {
        crono = JSON.parse(localStorage.getItem(keys[i]));
        cronos.push(crono);
    }
    return cronos;
}

function getTemplate(tmpl) {
    return $("#tmpl-" + tmpl).html();
}

function renderTemplate(tmpl, datos) {
    return Mustache.render(getTemplate(tmpl), datos);
}

function popupOpen(tmpl, datos) {
    $("#popup").html(Mustache.render(getTemplate(tmpl), datos));
    $("#popup").popup("open").popup('reposition', {'positionTo': 'window'});
}

function popupClose() {
    $("#popup").popup("close");
}

function logs(datos) {
    var datos_str = JSON.stringify(datos);

    // Append all data
    var timestamp = new Date().getTime();
    localStorage.setItem(codigo + '-' + timestamp, datos_str);
}

function tiempoToUnix(tiempo) {
    var tiempoSplit = tiempo.split(" ");

    if (tiempoSplit.length == 1) {
        var tempHora = tiempoSplit[0].replace(".", ":").split(":");
        var date = new Date();
        date.setHours(tempHora[0]);
        date.setMinutes(tempHora[1]);
        date.setSeconds(tempHora[2]);
        date.setMilliseconds(tempHora[3]);

    } else {
        var tempDia = tiempoSplit[0].split("-");
        var tempHora = tiempoSplit[1].replace(".", ":").split(":");
        var date = new Date(tempDia[0], (tempDia[1] - 1), tempDia[2], tempHora[0], tempHora[1], tempHora[2], tempHora[3]);
    }

    return date.getTime();
}

function unixToTiempo(unix) {
    var date = new Date(unix);
    var dia = date.getFullYear() + "-"
        + ((date.getMonth() < 9) ? "0" : "") + (date.getMonth()+1) + "-"
        + ((date.getDate() < 10) ? "0" : "") + date.getDate();
    var hora = ((date.getHours() < 10) ? "0" : "") + date.getHours() + ":"
        + ((date.getMinutes() < 10) ? "0" : "") + date.getMinutes() + ":"
        + ((date.getSeconds() < 10) ? "0" : "") + date.getSeconds() + "."
        + ((date.getMilliseconds() < 100) ? "0" : "") + ((date.getMilliseconds() < 10) ? "0" : "") + date.getMilliseconds();

    var tiempo = {
        dia: dia,
        hora: hora,
        tiempo: dia + " " + hora
    }

    return tiempo;
}

async function hash(message) {
    // encode as (utf-8) Uint8Array
    const msgUint8 = new TextEncoder().encode(message);
    // hash the message
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);
    // convert buffer to byte array
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    // convert bytes to hex string
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
}

function hexToAscii(hex) {
    return Buffer.from(hex, 'hex').toString('ascii');
}

function actualizarView () {

    if ($("#listaParticipantes a.esperando").length)
        $("#multicrono").show();
    else
        $("#multicrono").hide();

    var reenviar_cantidad = parseInt($("#listaParticipantes a.re-enviar").length);
    $("#reenviar-cantidad").html(reenviar_cantidad);
    if (reenviar_cantidad)
        $("#reenviar-btn").css('background-color', 'orange');
    else
        $("#reenviar-btn").css('background-color', 'grey');

}

var delay = (function(){
    var timer = 0;
    return function(callback, ms){
        clearTimeout (timer);
        timer = setTimeout(callback, ms);
    };
})();

function isTagCodigo() {
    if ($("select[name=rfid_crono] option:selected").val() == 'tag_codigo')
        $("#tag_codigo").show();
    else
        $("#tag_codigo").hide();
}

function kiosko() {
    $.mobile.changePage( "#kiosko", { transition: "slidedown"} );
    setTimeout(() => {
        $.mobile.changePage( "#lecturas", { transition: "slideup"} );
    }, 15000);
}