{"sourceFile": "app/www/css/app.css", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1756851630043, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1757159361558, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -108,4 +108,8 @@\n #qr,\r\n #esperar {\r\n     height: 25px;\r\n  }\r\n+\r\n+ ul li {\r\n+    font-size: 45px;\r\n+}\n\\ No newline at end of file\n"}, {"date": 1757159471004, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -110,6 +110,6 @@\n     height: 25px;\r\n  }\r\n \r\n  ul li {\r\n-    font-size: 45px;\r\n+    font-size: 50px;\r\n }\n\\ No newline at end of file\n"}], "date": 1756851630043, "name": "Commit-0", "content": ".ui-page-theme-a .ui-btn:focus,\r\nhtml .ui-bar-a .ui-btn:focus,\r\nhtml .ui-body-a .ui-btn:focus,\r\nhtml body .ui-group-theme-a .ui-btn:focus,\r\nhtml head + body .ui-btn.ui-btn-a:focus,\r\n.ui-page-theme-a .ui-focus,\r\nhtml .ui-bar-a .ui-focus,\r\nhtml .ui-body-a .ui-focus,\r\nhtml body .ui-group-theme-a .ui-focus,\r\nhtml head + body .ui-btn-a.ui-focus,\r\nhtml head + body .ui-body-a.ui-focus {\r\n    box-shadow: 0px 0px 12px #f7931e;\r\n}\r\n\r\n.ui-overlay-a,\r\n.ui-page-theme-a,\r\n.ui-page-theme-a .ui-panel-wrapper {\r\n    background-color: #444;\r\n}\r\n\r\n.resultado {\r\n    font-size: 12px;\r\n}\r\n\r\n.accion {\r\n    font-size: 9px;\r\n    font-style: italic;\r\n}\r\n\r\n.idparticipante {\r\n    margin-left: -16px;\r\n    font-weight: bold;\r\n    color: #f7931e;\r\n}\r\n\r\n.contador {\r\n    margin-left: 25px;\r\n}\r\n\r\n#totales {\r\n    margin: 5px;\r\n    text-align: center;\r\n}\r\n\r\n#consola {\r\n    display: flex;\r\n    flex-direction: column-reverse;\r\n    height: 250px;\r\n    overflow: scroll;\r\n    background-color: black;\r\n    color: white;\r\n    text-align: center;\r\n    margin: 5px;\r\n    padding: 5px;\r\n}\r\n\r\n#crono-keys {\r\n    max-width: 400px;\r\n    margin: auto;\r\n}\r\n\r\n#crono-btn {\r\n    position: fixed;\r\n    bottom: 5px;\r\n    z-index: 1000;\r\n    width: 110px;\r\n    height: 110px;\r\n    left: 50%;\r\n    margin-left: -55px;\r\n}\r\n\r\n#crono {\r\n    border-top-left-radius: 50%;\r\n    border-top-right-radius: 50%;\r\n    border-bottom-right-radius: 50%;\r\n    width: 100%;\r\n    height: 108px;\r\n    margin: 0;\r\n    padding: 0;\r\n    border: none;\r\n    box-shadow: rgba(0, 0, 0, 0.65) 0px 5px 25px;\r\n}\r\n\r\n#crono img {\r\n    width: 100%;\r\n}\r\n\r\n.ui-footer {\r\n    box-shadow: rgba(0, 0, 0, 0.55) 0px 5px 15px;\r\n}\r\n\r\n.ui-footer-fixed {\r\n    z-index: 100;\r\n}\r\n\r\n#reenviar-btn {\r\n    float: right;\r\n    margin-right: 30px;\r\n    background: grey;\r\n}\r\n\r\n#multicrono {\r\n    background: orange;\r\n    display: block;\r\n}\r\n\r\n#buscador-btn,\r\n#qr,\r\n#esperar {\r\n    height: 25px;\r\n }\r\n"}]}