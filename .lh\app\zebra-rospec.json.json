{"sourceFile": "app/zebra-rospec.json", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1758058382790, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1758058390607, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n         \"ROSpecStartTrigger\": {\n           \"ROSpecStartTriggerType\": \"Immediate\"\n         },\n         \"ROSpecStopTrigger\": {\n-          \"ROSpecStopTriggerType\": \"Immediate\"\n+          \"ROSpecStopTriggerType\": \"Null\"\n         }\n       },\n       \"AISpec\": {\n         \"AntennaIDs\": [1, 2, 3, 4],\n"}, {"date": 1758058400768, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,9 @@\n           \"ROSpecStopTriggerType\": \"Null\"\n         }\n       },\n       \"AISpec\": {\n-        \"AntennaIDs\": [1, 2, 3, 4],\n+        \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8],\n         \"AISpecStopTrigger\": {\n           \"AISpecStopTriggerType\": \"Null\"\n         },\n         \"InventoryParameterSpec\": {\n"}, {"date": 1758058412448, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,10 @@\n       },\n       \"AISpec\": {\n         \"AntennaIDs\": [1, 2, 3, 4, 5, 6, 7, 8],\n         \"AISpecStopTrigger\": {\n-          \"AISpecStopTriggerType\": \"Null\"\n+          \"AISpecStopTriggerType\": \"Duration\",\n+          \"DurationTriggerValue\": 400\n         },\n         \"InventoryParameterSpec\": {\n           \"InventoryParameterSpecID\": 1,\n           \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n"}], "date": 1758058382789, "name": "Commit-0", "content": "{\n  \"$schema\": \"https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json\",\n  \"id\": 103,\n  \"type\": \"ADD_ROSPEC\",\n  \"data\": {\n    \"ROSpec\": {\n      \"ROSpecID\": 1,\n      \"Priority\": 0,\n      \"CurrentState\": \"Disabled\",\n      \"ROBoundarySpec\": {\n        \"ROSpecStartTrigger\": {\n          \"ROSpecStartTriggerType\": \"Immediate\"\n        },\n        \"ROSpecStopTrigger\": {\n          \"ROSpecStopTriggerType\": \"Immediate\"\n        }\n      },\n      \"AISpec\": {\n        \"AntennaIDs\": [1, 2, 3, 4],\n        \"AISpecStopTrigger\": {\n          \"AISpecStopTriggerType\": \"Null\"\n        },\n        \"InventoryParameterSpec\": {\n          \"InventoryParameterSpecID\": 1,\n          \"ProtocolID\": \"EPCGlobalClass1Gen2\"\n        }\n      },\n      \"ROReportSpec\": {\n        \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n        \"N\": 1,\n        \"TagReportContentSelector\": {\n          \"EnableROSpecID\": false,\n          \"EnableSpecIndex\": false,\n          \"EnableInventoryParameterSpecID\": false,\n          \"EnableAntennaID\": true,\n          \"EnableChannelIndex\": false,\n          \"EnablePeakRSSI\": true,\n          \"EnableFirstSeenTimestamp\": true,\n          \"EnableLastSeenTimestamp\": true,\n          \"EnableTagSeenCount\": true,\n          \"EnableAccessSpecID\": false\n        }\n      }\n    }\n  }\n}\n"}]}