var reader_zebra_fx9600 = {

    'nombre': 'Zebra FX9600 RFID Reader (LLRP) - TEST MODE',
    'config': {
        'enter': true,
        'host': '*************',
        'port': 5084, // Puerto estándar LLRP
        'interval': 25
    },

    'connection': null,
    'interval': null,
    'antena': 0,
    'tagsRecientes': [],
    'ultimoTag': '',
    'tiempoReciente': 10000, // En milisegundos
    'rospecId': 1,
    'isConnected': false,
    'testMode': false,
    'stepByStep': false,
    'debugMode': false,
    'inicializando': false,
    'rospecActivo': false,

    totales: {
        barrido: 1,
        total: 0,
        diff: 0,
        ultimo: 0,
        antenas: [0,0,0,0,0,0,0,0,0],
    },

    conectar: function () {
        var that = this;

        console.log('🔵 ZEBRA FX9600 - INICIANDO CONEXIÓN');
        console.log('📍 Host:', this.config.host, 'Puerto:', this.config.port);

        this.tagsRecientes = [];
        this.antena = 0;
        this.cambio = true;
        this.continuar = false;
        this.isConnected = false;
        this.totales = {
            barrido: 0,
            total: 0,
            diff: 0,
            ultimo: 0,
            antenas: [0,0,0,0,0,0,0,0,0],
        };

        if (this.testMode) {
            console.log('🧪 MODO TEST ACTIVADO - Esperando confirmación del usuario...');
            if (!confirm('🧪 ZEBRA FX9600 TEST MODE\n\n¿Continuar con la conexión?\n\nHost: ' + this.config.host + '\nPuerto: ' + this.config.port)) {
                console.log('❌ Conexión cancelada por el usuario');
                return;
            }
        }

        try {
            console.log('📦 Cargando librería llrpjs...');
            const { LLRPClient } = require('llrpjs');
            console.log('✅ Librería llrpjs cargada correctamente');

            // Crear cliente LLRP
            console.log('🔧 Creando cliente LLRP...');
            this.connection = new LLRPClient({
                host: this.config.host,
                port: this.config.port
            });
            console.log('✅ Cliente LLRP creado');

            // Configurar event handlers
            console.log('🎯 Configurando event handlers...');
            this.setupEventHandlers();
            console.log('✅ Event handlers configurados');

            // Iniciar conexión
            console.log('🚀 Iniciando conexión TCP...');
            this.connection.connect().then(() => {
                console.log('✅ CONEXIÓN TCP ESTABLECIDA');
                alerta(that.nombre + ' conectado en el host ' + that.config.host + ':' + that.config.port);
                that.isConnected = true;

                if (that.testMode && that.stepByStep) {
                    console.log('⏸️ PAUSA - Conexión establecida. ¿Continuar con inicialización?');
                    setTimeout(() => {
                        if (confirm('✅ Conexión TCP establecida.\n\n¿Continuar con la inicialización del reader?')) {
                            that.inicializarReader();
                        } else {
                            console.log('⏸️ Inicialización pausada por el usuario');
                        }
                    }, 1000);
                } else {
                    that.inicializarReader();
                }
            }).catch((error) => {
                console.error('❌ ERROR EN CONEXIÓN TCP:', error);
                hardware.desconectar();
                let messageToUser = 'No se pudo conectar al reader Zebra FX9600.\nRevise la configuración de red y que el reader esté encendido.\nError: ' + error.message;
                alert(messageToUser);
            });

        } catch (error) {
            console.error('❌ ERROR AL INICIALIZAR:', error);
            hardware.desconectar();
            let messageToUser = 'Error al inicializar el reader Zebra FX9600.\nAsegúrese de que la librería LLRP esté instalada.\nError: ' + error.message;
            alert(messageToUser);
        }

        $("#totales").show();
    },

    setupEventHandlers: function() {
        var that = this;
        console.log('🎯 Configurando event handlers LLRP...');

        this.connection.on('error', function(error) {
            console.error('❌ ERROR DE CONEXIÓN:', error);
            hardware.desconectar();
            let messageToUser = 'Error de conexión con el reader Zebra FX9600.\nRevise la configuración de red.\nError: ' + error.message;
            alert(messageToUser);
        });

        this.connection.on('connect', function() {
            console.log('🔗 Evento CONNECT recibido');
            alertaConsola('Zebra FX9600 conectado via LLRP');
        });

        this.connection.on('disconnect', function() {
            console.log('🔌 Evento DISCONNECT recibido');
            that.isConnected = false;
            alertaConsola('Zebra FX9600 desconectado');

            if (that.testMode) {
                console.log('⚠️ Desconexión inesperada en modo test');
                alert('⚠️ Reader desconectado inesperadamente\n\nRevise la conexión de red con el reader.');
            }
        });

        // Handler para reportes de tags RFID
        this.connection.on('RO_ACCESS_REPORT', function(msg) {
            console.log('📡 RO_ACCESS_REPORT recibido');
            if (that.testMode) {
                console.log('📊 Datos del reporte:', msg);
            }
            that.procesarReporteAcceso(msg);
        });

        // Handler para notificaciones de eventos del reader
        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {
            console.log('📢 READER_EVENT_NOTIFICATION recibido');
            if (that.testMode) {
                console.log('📋 Datos del evento:', msg);
            }
            that.procesarEventoReader(msg);
        });

        console.log('✅ Event handlers configurados correctamente');
    },

    inicializarReader: async function() {
        console.log('🔧 INICIANDO INICIALIZACIÓN DEL READER...');

        try {
            const { LLRPCore } = require('llrpjs');
            console.log('📦 LLRPCore cargado correctamente');

            if (this.testMode && this.stepByStep) {
                console.log('⏸️ PAUSA - ¿Continuar con confirmación de conexión?');
                if (!confirm('🔧 Inicialización del reader\n\nPaso 1: Esperar confirmación de conexión\n\n¿Continuar?')) {
                    console.log('⏸️ Proceso pausado por el usuario');
                    return;
                }
            }

            // Esperar confirmación de conexión
            console.log('⏳ Esperando confirmación de conexión...');
            await this.esperarConfirmacionConexion();
            console.log('✅ Confirmación de conexión recibida');

            if (this.testMode && this.stepByStep) {
                console.log('⏸️ PAUSA - ¿Continuar con eliminación de ROSpecs?');
                if (!confirm('✅ Conexión confirmada\n\nPaso 2: Eliminar ROSpecs existentes\n\n¿Continuar?')) {
                    console.log('⏸️ Proceso pausado por el usuario');
                    return;
                }
            }

            // BLOQUEAR DELETE_ROSPEC que puede estar causando el colgado
            console.log('🛑 [CRITICAL] SALTANDO DELETE_ROSPEC para evitar colgado');
            console.log('⚠️ [DEBUG] Continuando sin eliminar ROSpecs existentes...');

            if (this.testMode && this.stepByStep) {
                console.log('⏸️ PAUSA - ¿Continuar con reset de fábrica?');
                if (!confirm('✅ ROSpecs eliminados\n\nPaso 3: Reset a configuración de fábrica\n\n¿Continuar?')) {
                    console.log('⏸️ Proceso pausado por el usuario');
                    return;
                }
            }

            // BLOQUEAR Factory Reset que está causando el colgado
            console.log('� [CRITICAL] SALTANDO SET_READER_CONFIG (Factory Reset) para evitar colgado');
            console.log('⚠️ [DEBUG] Continuando sin reset de fábrica...');

            if (this.testMode && this.stepByStep) {
                console.log('⏸️ PAUSA - ¿Continuar con configuración de ROSpec?');
                if (!confirm('✅ Reset aplicado\n\nPaso 4: Configurar ROSpec para lectura\n\n¿Continuar?')) {
                    console.log('⏸️ Proceso pausado por el usuario');
                    return;
                }
            }

            // Configurar ROSpec para lectura continua
            console.log('📋 Configurando ROSpec...');
            await this.configurarROSpec();
            console.log('✅ ROSpec configurado');

            console.log('🎉 READER INICIALIZADO CORRECTAMENTE');
            alerta('Reader Zebra FX9600 inicializado correctamente');

            if (this.testMode && this.stepByStep) {
                console.log('⏸️ PAUSA - ¿Finalizar inicialización?');
                if (confirm('🎉 Reader inicializado\n\n¿Finalizar inicialización? El reader ya está listo para detectar tags.')) {
                    this.finalizarInicializacion();
                } else {
                    console.log('⏸️ Finalización pausada por el usuario');
                }
            } else {
                // Finalizar inicialización
                this.finalizarInicializacion();
            }

        } catch (error) {
            console.error('❌ ERROR EN INICIALIZACIÓN:', error);
            alerta('Error al inicializar reader: ' + error.message);
        }
    },

    finalizarInicializacion: function() {
        console.log('🏁 [DEBUG] Finalizando inicialización...');

        // Simular respuesta de firmware para compatibilidad con la interfaz
        alerta('Firmware: LLRP 1.1 (Zebra FX9600)');

        // Configurar modo según window.rfid_crono
        console.log('📊 [DEBUG] Modo RFID configurado:', window.rfid_crono);

        if (window.rfid_crono == 'fast') {
            console.log('⚡ [DEBUG] Modo FAST - lectura continua en todas las antenas');
        } else {
            console.log('📡 [DEBUG] Modo NORMAL - lectura continua con identificación de antenas');
            this.antena = 0;
        }

        console.log('🎉 [DEBUG] Reader completamente listo para detectar tags');
        console.log('🎯 [DEBUG] Esperando tags RFID...');
    },

    esperarConfirmacionConexion: async function() {
        const { LLRPCore } = require('llrpjs');
        console.log('⏳ Esperando mensaje de confirmación...');

        try {
            let msg = await this.connection.recv(10000); // Aumentamos timeout a 10 segundos
            console.log('📨 Mensaje recibido:', msg.getName());

            if (msg instanceof LLRPCore.READER_EVENT_NOTIFICATION) {
                console.log('✅ READER_EVENT_NOTIFICATION recibido');
                const status = msg.getReaderEventNotificationData().getConnectionAttemptEvent()?.getStatus();
                if (status != "Success") {
                    throw new Error(`Verificación de conexión falló: ${status}`);
                }
                console.log('✅ Estado de conexión: Success');

            } else if (msg.getName() === 'KEEPALIVE') {
                console.log('💓 KEEPALIVE recibido - conexión establecida correctamente');
                // KEEPALIVE es una señal válida de que la conexión está activa

            } else {
                console.log('⚠️ Mensaje inesperado pero continuando:', msg.getName());
                // Algunos readers pueden enviar otros mensajes iniciales
                // En lugar de fallar, continuamos y verificamos la conectividad de otra manera
            }

            console.log('✅ Confirmación de conexión completada');

        } catch (error) {
            console.error('❌ Error esperando confirmación:', error);
            // En lugar de fallar completamente, intentamos continuar
            console.log('⚠️ Continuando sin confirmación explícita...');
        }
    },

    configurarROSpec: async function() {
        console.log('🚨 [CRITICAL] configurarROSpec() llamada!');
        console.log('🚨 [CRITICAL] Stack trace:', new Error().stack);
        console.log('🚨 [CRITICAL] rospecActivo:', this.rospecActivo);
        console.log('🚨 [CRITICAL] inicializando:', this.inicializando);

        if (this.rospecActivo) {
            console.log('🚨 [CRITICAL] ROSpec ya está activo, ABORTANDO para evitar duplicados');
            return;
        }

        const { LLRPCore } = require('llrpjs');

        // Obtener antenas habilitadas desde la configuración
        let antenasHabilitadas = this.obtenerAntenasHabilitadas();
        console.log('📡 Antenas habilitadas:', antenasHabilitadas);

        // Configuración ROSpec básica para lectura continua
        console.log('📋 [DEBUG] Usando configuración ROSpec por defecto (sin fs.readFileSync)');
        alertaConsola('Configurando ROSpec con ID: ' + this.rospecId + ' y antenas: ' + antenasHabilitadas.join(','));

        var rospecConfig = {
            data: {
                ROSpec: {
                    ROSpecID: this.rospecId,
                    Priority: 0,
                    CurrentState: "Disabled",
                    ROBoundarySpec: {
                        ROSpecStartTrigger: {
                            ROSpecStartTriggerType: "Immediate"
                        },
                        ROSpecStopTrigger: {
                            ROSpecStopTriggerType: "Null"
                        }
                    },
                    AISpec: {
                        AntennaIDs: antenasHabilitadas, // Usar antenas configuradas
                        AISpecStopTrigger: {
                            AISpecStopTriggerType: "Null"
                        },
                        InventoryParameterSpec: {
                            InventoryParameterSpecID: 1,
                            ProtocolID: "EPCGlobalClass1Gen2"
                        }
                    },
                    ROReportSpec: {
                        ROReportTrigger: "Upon_N_Tags_Or_End_Of_AISpec",
                        N: 1, // Reportar cada tag inmediatamente
                        TagReportContentSelector: {
                            EnableROSpecID: false,
                            EnableSpecIndex: false,
                            EnableInventoryParameterSpecID: false,
                            EnableAntennaID: true,
                            EnableChannelIndex: false,
                            EnablePeakRSSI: true,
                            EnableFirstSeenTimestamp: true,
                            EnableLastSeenTimestamp: true,
                            EnableTagSeenCount: true,
                            EnableAccessSpecID: false
                        }
                    }
                }
            }
        };

        // Función helper para timeout
        const withTimeout = (promise, timeoutMs, operation) => {
            return Promise.race([
                promise,
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error(`Timeout en ${operation} (${timeoutMs}ms)`)), timeoutMs)
                )
            ]);
        };

        // Agregar ROSpec con timeout
        console.log('📋 [DEBUG] Enviando ADD_ROSPEC...');
        let addResponse = await withTimeout(
            this.connection.transact(new LLRPCore.ADD_ROSPEC(rospecConfig)),
            10000,
            'ADD_ROSPEC'
        );
        console.log('✅ [DEBUG] ADD_ROSPEC response:', addResponse.getName());

        // Habilitar ROSpec con timeout
        console.log('🔧 [DEBUG] Enviando ENABLE_ROSPEC...');
        let enableResponse = await withTimeout(
            this.connection.transact(new LLRPCore.ENABLE_ROSPEC({
                data: { ROSpecID: this.rospecId }
            })),
            10000,
            'ENABLE_ROSPEC'
        );
        console.log('✅ [DEBUG] ENABLE_ROSPEC response:', enableResponse.getName());

        // Iniciar ROSpec con timeout
        console.log('🚀 [DEBUG] Enviando START_ROSPEC...');
        let startResponse = await withTimeout(
            this.connection.transact(new LLRPCore.START_ROSPEC({
                data: { ROSpecID: this.rospecId }
            })),
            10000,
            'START_ROSPEC'
        );
        console.log('✅ [DEBUG] START_ROSPEC response:', startResponse.getName());
        console.log('🎉 [DEBUG] ROSpec iniciado - Reader listo para detectar tags');

        // Marcar ROSpec como activo
        this.rospecActivo = true;
    },

    desconectar: function () {
        if (this.connection && this.isConnected) {
            try {
                const { LLRPCore } = require('llrpjs');

                // Detener ROSpec si está activo
                this.connection.transact(new LLRPCore.STOP_ROSPEC({
                    data: { ROSpecID: this.rospecId }
                })).then(() => {
                    // Cerrar conexión
                    return this.connection.transact(new LLRPCore.CLOSE_CONNECTION());
                }).then(() => {
                    if (this.connection.socketWritable) {
                        this.connection.disconnect();
                    }
                }).catch((error) => {
                    console.log('Error al desconectar:', error);
                });

            } catch (error) {
                console.log('Error en desconexión:', error);
            }
        }

        this.isConnected = false;
        alerta(this.nombre + ' desconectado');
        alertaConsola(this.nombre + ' desconectado');
    },

    consultar: function (command) {
        console.log('📞 [DEBUG] COMANDO RECIBIDO:', command);
        console.log('🔧 [DEBUG] Modo test:', this.testMode);

        try {
            // Mantener compatibilidad con la interfaz existente
            // Los comandos se manejan a través de LLRP automáticamente

            if (command == 'firmware') {
                console.log('🔧 [DEBUG] Comando firmware - IGNORADO en LLRP');
                console.log('ℹ️ [DEBUG] En LLRP no necesitamos comando firmware como en Invelion');
                // En LLRP no necesitamos este comando, el reader ya está funcionando

            } else if (['test', 'real', 'tag', 'tag_codigo', 'fast'].includes(command)) {
                console.log('📡 [DEBUG] Modo de lectura configurado:', command);
                console.log('ℹ️ [DEBUG] En LLRP la lectura es continua, no requiere comandos de polling');
                // La lectura es continua en LLRP, no necesita comandos explícitos
                alertaConsola('Modo de lectura: ' + command);

            } else if (command == 'antena') {
                console.log('📡 [DEBUG] Comando antena - En LLRP no se requiere rotación manual');
                // En LLRP las antenas se configuran en el ROSpec
                // Solo actualizamos el contador para compatibilidad con stats
                this.nextAntena();

            } else {
                console.log('❓ [DEBUG] Comando no implementado para LLRP:', command);
                alertaConsola('Command ' + command + ' not implemented for LLRP');
            }

            console.log('✅ [DEBUG] consultar() completado para comando:', command);

        } catch (error) {
            console.error('❌ [DEBUG] Error en consultar():', error);
        }
    },

    procesarComandoFirmware: function() {
        console.log('🔧 [DEBUG] Procesando respuesta de firmware...');
        console.log('🔧 [DEBUG] Modo test:', this.testMode);
        console.log('🔧 [DEBUG] Step by step:', this.stepByStep);

        // Simular respuesta de firmware
        alerta('Firmware: LLRP 1.1 (Zebra FX9600)');
        console.log('✅ [DEBUG] Respuesta de firmware enviada');

        if (this.testMode && this.stepByStep) {
            console.log('⏸️ [DEBUG] PAUSA - Firmware procesado. ¿Continuar con siguiente paso?');
            setTimeout(() => {
                if (confirm('✅ Firmware procesado\n\nModo actual: ' + (window.rfid_crono || 'no definido') + '\n\n¿Continuar con el siguiente paso?')) {
                    this.continuarDespuesFirmware();
                } else {
                    console.log('⏸️ [DEBUG] Proceso pausado después de firmware');
                }
            }, 500);
        } else {
            console.log('🔄 [DEBUG] Modo normal - llamando continuarDespuesFirmware directamente');
            this.continuarDespuesFirmware();
        }
    },

    continuarDespuesFirmware: function() {
        console.log('🔄 [DEBUG] Continuando después de firmware...');
        console.log('📊 [DEBUG] Modo RFID actual:', window.rfid_crono);
        console.log('🔧 [DEBUG] Modo test actual:', this.testMode);

        try {
            if (window.rfid_crono == 'fast') {
                console.log('⚡ [DEBUG] Modo FAST detectado - lectura continua en todas las antenas');
                // En modo fast, LLRP ya está leyendo continuamente
                console.log('✅ [DEBUG] Reader configurado para modo FAST');
            } else {
                console.log('📡 [DEBUG] Modo normal detectado - lectura continua con rotación de antenas');
                // En LLRP no necesitamos rotar antenas manualmente como en Invelion
                // El ROSpec ya está configurado para leer de todas las antenas
                this.antena = 0;
                console.log('✅ [DEBUG] Reader configurado para modo NORMAL');
            }

            console.log('🎉 [DEBUG] Reader listo - esperando tags...');
            console.log('🎉 [DEBUG] continuarDespuesFirmware completado exitosamente');

        } catch (error) {
            console.error('❌ [DEBUG] Error en continuarDespuesFirmware:', error);
        }
    },

    procesarReporteAcceso: function(msg) {
        console.log('📡 PROCESANDO REPORTE DE ACCESO...');

        // HABILITAR procesamiento de tags si está en modo normal
        if (!this.testMode) {
            console.log('✅ [NORMAL] Procesando tags en modo normal');
        } else {
            console.log('🛑 [TEST] BLOQUEANDO procesamiento de tags en modo test');
            console.log('📊 Reporte recibido pero no procesado');
            return;
        }

        let tagReportDataList = msg.getTagReportData();
        if (!Array.isArray(tagReportDataList)) {
            tagReportDataList = [tagReportDataList];
        }

        console.log('📊 Número de tags en el reporte:', tagReportDataList.length);

        for (let i = 0; i < tagReportDataList.length; i++) {
            let tagReportData = tagReportDataList[i];
            console.log('🏷️ Procesando tag', i + 1, 'de', tagReportDataList.length);

            let epcParam = tagReportData.getEPCParameter();
            if (epcParam) {
                let epc = epcParam.getEPC();

                // Extraer correctamente el AntennaID
                let antennaId = 1; // Default
                try {
                    let antennaParam = tagReportData.getAntennaID();
                    if (antennaParam && typeof antennaParam === 'number') {
                        antennaId = antennaParam;
                    } else if (antennaParam && antennaParam.getAntennaID) {
                        antennaId = antennaParam.getAntennaID();
                    } else {
                        antennaId = this.antena || 1;
                    }
                } catch (e) {
                    console.log('⚠️ [DEBUG] Error extrayendo AntennaID, usando default:', e.message);
                    antennaId = this.antena || 1;
                }

                console.log('✅ TAG DETECTADO:');
                console.log('   📋 EPC:', epc);
                console.log('   📡 Antena:', antennaId);

                if (this.testMode && this.stepByStep) {
                    if (confirm('🏷️ TAG DETECTADO\n\nEPC: ' + epc + '\nAntena: ' + antennaId + '\n\n¿Procesar este tag?')) {
                        // Procesar el tag usando la función existente
                        this.epc(epc, antennaId);
                    } else {
                        console.log('⏸️ Procesamiento de tag pausado por el usuario');
                        continue;
                    }
                } else {
                    // Procesar el tag usando la función existente
                    this.epc(epc, antennaId);
                }
            } else {
                console.log('⚠️ Tag sin parámetro EPC válido');
            }
        }

        this.stats();
        console.log('✅ Reporte de acceso procesado completamente');
    },

    procesarEventoReader: function(msg) {
        console.log('📢 EVENTO DEL READER:', msg.getName());
        // Manejar eventos del reader
        alertaConsola('Evento del reader: ' + msg.getName());

        if (this.testMode) {
            console.log('📋 Detalles del evento:', msg);
        }
    },

    recibir: function (data) {
        // Esta función se mantiene por compatibilidad pero no se usa en LLRP
        // Los datos se reciben a través de los event handlers
        alertaConsola('Función recibir() no utilizada en LLRP');
    },

    epc: function (tagID, antena = false) {
        console.log('🏷️ [DEBUG] epc() llamada - tagID:', tagID, 'antena:', antena, 'modo:', window.rfid_crono);

        if (window.sonido)
            bep.play();
        alertaConsola('EPC ' + (antena || this.antena) + ' ' + tagID);
        if (this.testMode) {
            console.log('📋 Procesando tag:', tagID, 'en antena:', antena || this.antena);
            //return;
        }
        this.totales.ultimo++;
        this.totales.total++;
        if (!antena || window.rfid_crono != 'fast')
            antena = antena || this.antena || 1;
        this.totales.antenas[antena] = !isNaN(this.totales.antenas[antena])
            ? this.totales.antenas[antena] + 1
            : 1;
        if (!this.tagReciente(tagID)) {
            $("#tagID").html(tagID);
            let idparticipante = $("#idparticipante").html();
            if (!isNaN(idparticipante)
                && (window.rfid_crono == 'tag' || window.rfid_crono == 'tag_codigo'))
                app.tag(window.rfid_crono);

            else if (window.rfid_crono == 'real' || window.rfid_crono == 'fast' || window.rfid_crono == 'tag') {
                // BLOQUEAR COMPLETAMENTE app.crono() para evitar colgado
                console.log('� [CRITICAL] BLOQUEANDO app.crono() para evitar colgado');
                // Ejecutar app.crono() de forma asíncrona para evitar bloqueos
                setTimeout(() => {
                    try {
                        app.crono();
                    } catch (error) {
                        console.error('❌ Error en app.crono():', error);
                    }
                }, 0);

            } else {
                console.log('⏸️ [DEBUG] No se cumple condición para app.crono() - Modo:', window.rfid_crono);
            }
        }
    },

    stats: function () {
        if (window.rfid_crono == 'test' || window.rfid_crono == 'real' || window.rfid_crono == 'fast')
            $("#totales").html(renderTemplate('totales', {
                barrido: this.totales.barrido,
                total: this.totales.total,
                diff: this.tagsRecientes.length,
                ultimo: this.totales.ultimo,
                antenas: true,
                antena_1: window['antena_1'] ? this.totales.antenas[1] : '-',
                antena_2: window['antena_2'] ? this.totales.antenas[2] : '-',
                antena_3: window['antena_3'] ? this.totales.antenas[3] : '-',
                antena_4: window['antena_4'] ? this.totales.antenas[4] : '-',
                antena_5: window['antena_5'] ? this.totales.antenas[5] : '-',
                antena_6: window['antena_6'] ? this.totales.antenas[6] : '-',
                antena_7: window['antena_7'] ? this.totales.antenas[7] : '-',
                antena_8: window['antena_8'] ? this.totales.antenas[8] : '-',
            }));

        return;
    },

    tagReciente: function (tagID) {
        var time = new Date().getTime();

        for (i = 0; i < this.tagsRecientes.length; i++) {
            if (this.tagsRecientes[i].tagID == tagID) {
                if (this.tagsRecientes[i].time > (time - window.rebote)) {
                    return true;
                } else {
                    this.tagsRecientes[i].time = time;
                    return false;
                }
            }
        }

        let reciente = {'time': time, 'tagID': tagID};
        this.tagsRecientes.push(reciente);
        return false;
    },

    obtenerAntenasHabilitadas: function() {
        let antenas = [];

        // Verificar cada antena desde la configuración
        for (let i = 1; i <= 8; i++) {
            if (window['antena_' + i]) {
                antenas.push(i);
            }
        }

        // Si no hay antenas habilitadas, usar antena 1 por defecto
        if (antenas.length === 0) {
            console.log('⚠️ No hay antenas habilitadas, usando antena 1 por defecto');
            antenas = [1];
            window.antena_1 = true; // Asegurar que antena 1 esté habilitada
        }

        console.log('📡 Antenas configuradas:', antenas);
        return antenas;
    },

    nextAntena: function () {
        if (this.testMode) {
            console.log('📡 Calculando siguiente antena...');
        }
        let antena;
        do {
            if (this.antena >= 8) {
                this.antena = 1;
                this.totales.barrido++;
                this.totales.ultimo = 0;
                if (this.testMode) {
                    console.log('🔄 Reiniciando ciclo de antenas - Barrido:', this.totales.barrido);
                }
            } else {
                this.antena++;
            }
            antena = this.antena;
            if (this.testMode) {
                console.log('🔍 Verificando antena', antena, '- Habilitada:', !!window['antena_' + antena]);
            }
        } while (!window['antena_' + antena]);

        if (this.testMode) {
            console.log('✅ Antena seleccionada:', antena);
        }
        return antena;
    },

    // Función para desactivar el modo test
    desactivarModoTest: function() {
        console.log('🔧 DESACTIVANDO MODO TEST...');
        this.testMode = false;
        this.stepByStep = false;
        this.nombre = 'Zebra FX9600 RFID Reader (LLRP)';
        console.log('✅ Modo test desactivado - Funcionamiento normal');
        alerta('Modo test desactivado - Reader en funcionamiento normal');
    },

    // Función para activar el modo test
    activarModoTest: function() {
        console.log('🧪 ACTIVANDO MODO TEST...');
        this.testMode = true;
        this.stepByStep = true;
        this.nombre = 'Zebra FX9600 RFID Reader (LLRP) - TEST MODE';
        console.log('✅ Modo test activado');
        alerta('Modo test activado - Se solicitará confirmación en cada paso');
    },

    // Función para reconectar si se perdió la conexión
    reconectar: async function() {
        console.log('🔄 RECONECTANDO AL READER...');

        try {
            const { LLRPClient } = require('llrpjs');

            // Limpiar conexión anterior si existe
            if (this.connection) {
                try {
                    this.connection.disconnect();
                } catch (e) {
                    console.log('⚠️ Error cerrando conexión anterior:', e.message);
                }
            }

            // Crear nueva conexión
            console.log('🔧 Creando nueva conexión...');
            this.connection = new LLRPClient({
                host: this.config.host,
                port: this.config.port
            });

            // Configurar event handlers
            this.setupEventHandlers();

            // Conectar
            console.log('🚀 Conectando...');
            await this.connection.connect();
            console.log('✅ Reconexión exitosa');

            this.isConnected = true;
            return true;

        } catch (error) {
            console.error('❌ Error en reconexión:', error);
            this.isConnected = false;
            return false;
        }
    },

    // Función para inicialización rápida (omite DELETE_ROSPEC y RESET)
    inicializacionRapida: async function() {
        console.log('� [CRITICAL] inicializacionRapida() llamada AUTOMÁTICAMENTE!');
        console.log('� [CRITICAL] Stack trace completo:', new Error().stack);
        console.log('🚨 [CRITICAL] Estado actual:');
        console.log('   - inicializando:', this.inicializando);
        console.log('   - isConnected:', this.isConnected);
        console.log('   - rospecActivo:', this.rospecActivo);

        // BLOQUEAR COMPLETAMENTE las llamadas automáticas
        console.log('🛑 [CRITICAL] BLOQUEANDO inicializacionRapida() automática');
        return;

        if (this.inicializando) {
            console.log('⚠️ [DEBUG] Inicialización ya en progreso, ignorando...');
            return;
        }

        if (this.isConnected && this.rospecActivo) {
            console.log('⚠️ [DEBUG] Reader ya está funcionando, ignorando inicialización...');
            return;
        }

        this.inicializando = true;
        console.log('🚀 INICIALIZACIÓN RÁPIDA - Omitiendo pasos problemáticos...');

        try {
            // Verificar conexión
            if (!this.connection || !this.isConnected) {
                console.log('🔄 Conexión perdida, reconectando...');
                let reconectado = await this.reconectar();
                if (!reconectado) {
                    throw new Error('No se pudo reconectar al reader');
                }

                // Esperar un poco después de reconectar
                console.log('⏳ Esperando estabilización de conexión...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Ir directamente a configurar ROSpec
            console.log('📋 Configurando ROSpec directamente...');
            await this.configurarROSpec();
            console.log('✅ ROSpec configurado');

            console.log('🎉 INICIALIZACIÓN RÁPIDA COMPLETADA');
            alerta('Reader Zebra FX9600 inicializado (modo rápido)');

            // Finalizar inicialización sin comando firmware
            this.finalizarInicializacion();

        } catch (error) {
            console.error('❌ ERROR EN INICIALIZACIÓN RÁPIDA:', error);
            alerta('Error en inicialización rápida: ' + error.message);
        } finally {
            this.inicializando = false;
        }
    },

    // Función mínima para configurar ROSpec sin operaciones problemáticas
    configurarROSpecMinimo: async function() {
        console.log('🚀 [MINIMAL] Configuración ROSpec mínima iniciada');

        if (!this.connection) {
            throw new Error('No hay conexión LLRP activa');
        }

        const { LLRPCore } = require('llrpjs');

        // Configuración ROSpec ultra-simple
        let antenasHabilitadas = [1]; // Solo antena 1 para simplificar
        console.log('📡 [MINIMAL] Usando solo antena 1 para prueba');

        var rospecConfig = {
            data: {
                ROSpec: {
                    ROSpecID: this.rospecId,
                    Priority: 0,
                    CurrentState: "Disabled",
                    ROBoundarySpec: {
                        ROSpecStartTrigger: {
                            ROSpecStartTriggerType: "Immediate"
                        },
                        ROSpecStopTrigger: {
                            ROSpecStopTriggerType: "Null"
                        }
                    },
                    AISpec: {
                        AntennaIDs: antenasHabilitadas,
                        AISpecStopTrigger: {
                            AISpecStopTriggerType: "Null"
                        },
                        InventoryParameterSpec: {
                            InventoryParameterSpecID: 1,
                            ProtocolID: "EPCGlobalClass1Gen2"
                        }
                    },
                    ROReportSpec: {
                        ROReportTrigger: "Upon_N_Tags_Or_End_Of_AISpec",
                        N: 1,
                        TagReportContentSelector: {
                            EnableROSpecID: false,
                            EnableSpecIndex: false,
                            EnableInventoryParameterSpecID: false,
                            EnableAntennaID: true,
                            EnableChannelIndex: false,
                            EnablePeakRSSI: false,
                            EnableFirstSeenTimestamp: false,
                            EnableLastSeenTimestamp: false,
                            EnableTagSeenCount: false,
                            EnableAccessSpecID: false
                        }
                    }
                }
            }
        };

        // Timeout muy agresivo para evitar colgados
        const timeoutMs = 5000; // 5 segundos
        const withTimeout = (promise, operation) => {
            return Promise.race([
                promise,
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error(`Timeout en ${operation} (${timeoutMs}ms)`)), timeoutMs)
                )
            ]);
        };

        try {
            // ADD_ROSPEC con timeout agresivo
            console.log('📋 [MINIMAL] Enviando ADD_ROSPEC...');
            let addResponse = await withTimeout(
                this.connection.transact(new LLRPCore.ADD_ROSPEC(rospecConfig)),
                'ADD_ROSPEC'
            );
            console.log('✅ [MINIMAL] ADD_ROSPEC exitoso');

            // ENABLE_ROSPEC
            console.log('🔧 [MINIMAL] Enviando ENABLE_ROSPEC...');
            await withTimeout(
                this.connection.transact(new LLRPCore.ENABLE_ROSPEC({
                    data: { ROSpecID: this.rospecId }
                })),
                'ENABLE_ROSPEC'
            );
            console.log('✅ [MINIMAL] ENABLE_ROSPEC exitoso');

            // START_ROSPEC
            console.log('🚀 [MINIMAL] Enviando START_ROSPEC...');
            await withTimeout(
                this.connection.transact(new LLRPCore.START_ROSPEC({
                    data: { ROSpecID: this.rospecId }
                })),
                'START_ROSPEC'
            );
            console.log('✅ [MINIMAL] START_ROSPEC exitoso');

            this.rospecActivo = true;
            console.log('🎉 [MINIMAL] ROSpec configurado y activo');

        } catch (error) {
            console.error('❌ [MINIMAL] Error en configuración:', error.message);
            throw error;
        }
    },

    // Función para consultar ROSpecs existentes
    consultarROSpecs: async function() {
        console.log('🔍 CONSULTANDO ROSpecs existentes...');

        if (!this.connection) {
            console.log('❌ No hay conexión LLRP activa');
            return;
        }

        try {
            const { LLRPCore } = require('llrpjs');

            // GET_ROSPECS para ver todos los ROSpecs
            console.log('📤 Enviando GET_ROSPECS...');
            let response = await Promise.race([
                this.connection.transact(new LLRPCore.GET_ROSPECS()),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)
                )
            ]);

            console.log('✅ GET_ROSPECS response:', response.getName());

            // Analizar la respuesta
            let rospecs = response.getROSpec ? response.getROSpec() : [];
            if (!Array.isArray(rospecs)) {
                rospecs = rospecs ? [rospecs] : [];
            }

            console.log('📊 ROSpecs encontrados:', rospecs.length);

            for (let i = 0; i < rospecs.length; i++) {
                let rospec = rospecs[i];
                console.log(`📋 ROSpec ${i + 1}:`);
                console.log(`   - ID: ${rospec.getROSpecID ? rospec.getROSpecID() : 'N/A'}`);
                console.log(`   - Estado: ${rospec.getCurrentState ? rospec.getCurrentState() : 'N/A'}`);
                console.log(`   - Prioridad: ${rospec.getPriority ? rospec.getPriority() : 'N/A'}`);
            }

            // GET_READER_CONFIG para ver configuración general
            console.log('📤 Enviando GET_READER_CONFIG...');
            let configResponse = await Promise.race([
                this.connection.transact(new LLRPCore.GET_READER_CONFIG({
                    data: { RequestedData: "All" }
                })),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout en GET_READER_CONFIG')), 5000)
                )
            ]);

            console.log('✅ GET_READER_CONFIG response:', configResponse.getName());
            console.log('📊 Configuración del reader obtenida');

            return { rospecs, config: configResponse };

        } catch (error) {
            console.error('❌ Error consultando ROSpecs:', error.message);
        }
    },

    // Función para limpiar ROSpecs específicos
    limpiarROSpec: async function(rospecId) {
        console.log('🗑️ LIMPIANDO ROSpec ID:', rospecId);

        if (!this.connection) {
            console.log('❌ No hay conexión LLRP activa');
            return;
        }

        try {
            const { LLRPCore } = require('llrpjs');

            // Primero detener el ROSpec si está activo
            console.log('⏹️ Deteniendo ROSpec...');
            await this.connection.transact(new LLRPCore.STOP_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            // Luego deshabilitarlo
            console.log('🔧 Deshabilitando ROSpec...');
            await this.connection.transact(new LLRPCore.DISABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            // Finalmente eliminarlo
            console.log('🗑️ Eliminando ROSpec...');
            await this.connection.transact(new LLRPCore.DELETE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            console.log('✅ ROSpec eliminado correctamente');

        } catch (error) {
            console.log('⚠️ Error limpiando ROSpec (puede ser normal):', error.message);
        }
    },

    // Función para activar un ROSpec existente
    activarROSpec: async function(rospecId) {
        console.log('🚀 ACTIVANDO ROSpec ID:', rospecId);

        if (!this.connection) {
            console.log('❌ No hay conexión LLRP activa');
            return;
        }

        try {
            const { LLRPCore } = require('llrpjs');

            // Habilitar ROSpec
            console.log('🔧 Habilitando ROSpec...');
            await this.connection.transact(new LLRPCore.ENABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
            console.log('✅ ROSpec habilitado');

            // Iniciar ROSpec
            console.log('🚀 Iniciando ROSpec...');
            await this.connection.transact(new LLRPCore.START_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
            console.log('✅ ROSpec iniciado');

            // Marcar como activo
            this.rospecActivo = true;
            this.rospecId = rospecId;

            console.log('🎉 ROSpec activo - Reader listo para detectar tags');

            // Finalizar inicialización
            this.finalizarInicializacion();

        } catch (error) {
            console.error('❌ Error activando ROSpec:', error.message);
        }
    },

    // Función inteligente que maneja cualquier estado del reader
    inicializacionInteligente: async function() {
        console.log('🧠 INICIALIZACIÓN INTELIGENTE');

        if (!this.connection) {
            console.log('❌ No hay conexión activa');
            return false;
        }

        try {
            // Paso 1: Analizar estado actual
            console.log('🔍 Analizando estado del reader...');
            const estado = await this.analizarEstadoReader();

            if (!estado) {
                throw new Error('No se pudo analizar el estado del reader');
            }

            // Paso 2: Decidir estrategia basada en el análisis
            const estrategia = this.decidirEstrategia(estado);
            console.log('📋 Estrategia:', estrategia.accion);
            console.log('📝 Descripción:', estrategia.descripcion);

            // Paso 3: Ejecutar estrategia
            await this.ejecutarEstrategiaInteligente(estrategia, estado);

            // Paso 4: Finalizar
            this.rospecActivo = true;
            this.finalizarInicializacion();

            console.log('✅ Inicialización inteligente completada');
            return true;

        } catch (error) {
            console.error('❌ Error en inicialización inteligente:', error.message);
            return false;
        }
    },

    // Función para analizar el estado completo del reader
    analizarEstadoReader: async function() {
        try {
            const { LLRPCore } = require('llrpjs');

            // Obtener ROSpecs
            let rospecsResponse = await Promise.race([
                this.connection.transact(new LLRPCore.GET_ROSPECS()),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)
                )
            ]);

            // Procesar ROSpecs
            let rospecs = rospecsResponse.getROSpec ? rospecsResponse.getROSpec() : [];
            if (!Array.isArray(rospecs)) {
                rospecs = rospecs ? [rospecs] : [];
            }

            const rospecsInfo = rospecs.map(rospec => ({
                id: rospec.getROSpecID ? rospec.getROSpecID() : null,
                estado: rospec.getCurrentState ? rospec.getCurrentState() : 'Unknown',
                prioridad: rospec.getPriority ? rospec.getPriority() : 0
            }));

            // Obtener antenas configuradas
            const antenasDeseadas = this.obtenerAntenasHabilitadas();

            console.log('📊 Estado del reader:');
            console.log(`   - Total ROSpecs: ${rospecsInfo.length}`);
            console.log(`   - Antenas deseadas: ${antenasDeseadas.join(', ')}`);

            rospecsInfo.forEach((rospec, i) => {
                console.log(`   - ROSpec ${i + 1}: ID=${rospec.id}, Estado=${rospec.estado}`);
            });

            return {
                rospecs: rospecsInfo,
                antenasDeseadas: antenasDeseadas,
                total: rospecsInfo.length,
                activos: rospecsInfo.filter(r => r.estado === 'Active').length,
                deshabilitados: rospecsInfo.filter(r => r.estado === 'Disabled').length
            };

        } catch (error) {
            console.error('❌ Error analizando estado:', error.message);
            return null;
        }
    },

    // Función para decidir qué estrategia usar
    decidirEstrategia: function(estado) {
        const { rospecs, total, activos, deshabilitados } = estado;

        if (total === 0) {
            // Caso 1: No hay ROSpecs
            return {
                accion: 'CREAR_NUEVO',
                descripcion: 'No hay ROSpecs, crear uno nuevo',
                rospecId: Math.floor(Math.random() * 1000) + 100
            };
        }

        if (activos === 1) {
            // Caso 2: Hay exactamente 1 ROSpec activo (ideal)
            const rospecActivo = rospecs.find(r => r.estado === 'Active');
            return {
                accion: 'USAR_EXISTENTE',
                descripcion: 'Hay 1 ROSpec activo, usar ese',
                rospecId: rospecActivo.id
            };
        }

        if (activos > 1) {
            // Caso 3: Múltiples ROSpecs activos (problema)
            const rospecsActivos = rospecs.filter(r => r.estado === 'Active');
            return {
                accion: 'LIMPIAR_Y_USAR_UNO',
                descripcion: `Hay ${activos} ROSpecs activos, limpiar y usar uno`,
                rospecId: rospecsActivos[0].id,
                rospecsAEliminar: rospecsActivos.slice(1).map(r => r.id)
            };
        }

        if (deshabilitados > 0) {
            // Caso 4: Solo hay ROSpecs deshabilitados
            const rospecDeshabilitado = rospecs.find(r => r.estado === 'Disabled');
            return {
                accion: 'ACTIVAR_EXISTENTE',
                descripcion: 'Hay ROSpecs deshabilitados, activar uno',
                rospecId: rospecDeshabilitado.id
            };
        }

        // Caso por defecto
        return {
            accion: 'CREAR_NUEVO',
            descripcion: 'Estado desconocido, crear ROSpec nuevo',
            rospecId: Math.floor(Math.random() * 1000) + 100
        };
    },

    // Función para ejecutar la estrategia seleccionada
    ejecutarEstrategiaInteligente: async function(estrategia, estado) {
        const { LLRPCore } = require('llrpjs');

        switch (estrategia.accion) {
            case 'USAR_EXISTENTE':
                console.log('✅ Usando ROSpec existente ID:', estrategia.rospecId);
                this.rospecId = estrategia.rospecId;
                break;

            case 'CREAR_NUEVO':
                console.log('🆕 Creando nuevo ROSpec ID:', estrategia.rospecId);
                await this.crearROSpecConAntenas(estrategia.rospecId, estado.antenasDeseadas);
                break;

            case 'ACTIVAR_EXISTENTE':
                console.log('🔧 Activando ROSpec existente ID:', estrategia.rospecId);
                await this.activarROSpecSilencioso(estrategia.rospecId);
                break;

            case 'LIMPIAR_Y_USAR_UNO':
                console.log('🧹 Limpiando ROSpecs duplicados...');

                // Eliminar ROSpecs extras
                for (const rospecId of estrategia.rospecsAEliminar) {
                    console.log('🗑️ Eliminando ROSpec duplicado ID:', rospecId);
                    await this.eliminarROSpecSilencioso(rospecId);
                }

                console.log('✅ Usando ROSpec principal ID:', estrategia.rospecId);
                this.rospecId = estrategia.rospecId;
                break;

            default:
                throw new Error('Estrategia desconocida: ' + estrategia.accion);
        }
    },

    // Función para eliminar ROSpec silenciosamente
    eliminarROSpecSilencioso: async function(rospecId) {
        const { LLRPCore } = require('llrpjs');

        try {
            // Intentar detener (puede fallar si no está activo)
            try {
                await this.connection.transact(new LLRPCore.STOP_ROSPEC({
                    data: { ROSpecID: rospecId }
                }));
            } catch (e) { /* Ignorar errores de STOP */ }

            // Intentar deshabilitar (puede fallar si no está habilitado)
            try {
                await this.connection.transact(new LLRPCore.DISABLE_ROSPEC({
                    data: { ROSpecID: rospecId }
                }));
            } catch (e) { /* Ignorar errores de DISABLE */ }

            // Eliminar
            await this.connection.transact(new LLRPCore.DELETE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            console.log('✅ ROSpec', rospecId, 'eliminado correctamente');

        } catch (error) {
            console.log('⚠️ Error eliminando ROSpec', rospecId, ':', error.message);
        }
    },

    // Función para activar ROSpec sin logs excesivos
    activarROSpecSilencioso: async function(rospecId) {
        const { LLRPCore } = require('llrpjs');

        try {
            // Habilitar ROSpec
            await this.connection.transact(new LLRPCore.ENABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            // Iniciar ROSpec
            await this.connection.transact(new LLRPCore.START_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            this.rospecActivo = true;
            this.rospecId = rospecId;
            console.log('✅ ROSpec', rospecId, 'activado correctamente');

        } catch (error) {
            console.error('❌ Error activando ROSpec:', error.message);
            throw error;
        }
    },

    // Función para crear ROSpec con antenas específicas
    crearROSpecConAntenas: async function(rospecId, antenas) {
        const { LLRPCore } = require('llrpjs');

        console.log('🆕 Creando ROSpec ID:', rospecId, 'con antenas:', antenas);

        const rospecConfig = {
            data: {
                ROSpec: {
                    ROSpecID: rospecId,
                    Priority: 0,
                    CurrentState: "Disabled",
                    ROBoundarySpec: {
                        ROSpecStartTrigger: {
                            ROSpecStartTriggerType: "Immediate"
                        },
                        ROSpecStopTrigger: {
                            ROSpecStopTriggerType: "Null"
                        }
                    },
                    AISpec: {
                        AntennaIDs: antenas,
                        AISpecStopTrigger: {
                            AISpecStopTriggerType: "Null"
                        },
                        InventoryParameterSpec: {
                            InventoryParameterSpecID: 1,
                            ProtocolID: "EPCGlobalClass1Gen2"
                        }
                    },
                    ROReportSpec: {
                        ROReportTrigger: "Upon_N_Tags_Or_End_Of_AISpec",
                        N: 1,
                        TagReportContentSelector: {
                            EnableROSpecID: false,
                            EnableSpecIndex: false,
                            EnableInventoryParameterSpecID: false,
                            EnableAntennaID: true,
                            EnableChannelIndex: false,
                            EnablePeakRSSI: true,
                            EnableFirstSeenTimestamp: false,
                            EnableLastSeenTimestamp: false,
                            EnableTagSeenCount: false,
                            EnableAccessSpecID: false
                        }
                    }
                }
            }
        };

        try {
            // Crear ROSpec
            await this.connection.transact(new LLRPCore.ADD_ROSPEC(rospecConfig));
            console.log('✅ ROSpec creado');

            // Activar ROSpec
            await this.activarROSpecSilencioso(rospecId);

        } catch (error) {
            console.error('❌ Error creando ROSpec:', error.message);
            throw error;
        }
    },

    // Función simple para limpiar ROSpecs duplicados (sin async/await)
    limpiarROSpecsDuplicados: function() {
        console.log('🧹 LIMPIANDO ROSpecs duplicados...');

        if (!this.connection) {
            console.log('❌ No hay conexión activa');
            return;
        }

        const { LLRPCore } = require('llrpjs');

        // Obtener ROSpecs actuales
        this.connection.transact(new LLRPCore.GET_ROSPECS())
            .then((response) => {
                console.log('✅ GET_ROSPECS response recibido');

                // Procesar ROSpecs
                let rospecs = response.getROSpec ? response.getROSpec() : [];
                if (!Array.isArray(rospecs)) {
                    rospecs = rospecs ? [rospecs] : [];
                }

                const rospecsActivos = rospecs.filter(rospec => {
                    const estado = rospec.getCurrentState ? rospec.getCurrentState() : 'Unknown';
                    return estado === 'Active';
                });

                console.log('📊 ROSpecs activos encontrados:', rospecsActivos.length);

                if (rospecsActivos.length <= 1) {
                    console.log('✅ No hay ROSpecs duplicados');
                    if (rospecsActivos.length === 1) {
                        const rospecId = rospecsActivos[0].getROSpecID();
                        console.log('✅ Usando ROSpec activo ID:', rospecId);
                        this.rospecActivo = true;
                        this.rospecId = rospecId;
                        this.finalizarInicializacion();
                    } else {
                        console.log('⚠️ No hay ROSpecs activos. Ejecuta zebraTest.minima() para crear uno');
                    }
                    return;
                }

                // Hay múltiples ROSpecs activos - eliminar duplicados
                console.log('🗑️ Eliminando', rospecsActivos.length - 1, 'ROSpecs duplicados...');

                // Mantener el primer ROSpec, eliminar el resto
                const rospecPrincipal = rospecsActivos[0];
                const rospecsAEliminar = rospecsActivos.slice(1);

                // Eliminar ROSpecs duplicados uno por uno
                this.eliminarROSpecsSecuencial(rospecsAEliminar, 0, () => {
                    // Cuando termine de eliminar, usar el ROSpec principal
                    const rospecId = rospecPrincipal.getROSpecID();
                    console.log('✅ Usando ROSpec principal ID:', rospecId);
                    this.rospecActivo = true;
                    this.rospecId = rospecId;
                    this.finalizarInicializacion();
                    console.log('🎉 Limpieza completada - Reader listo para detectar tags');
                });

            })
            .catch((error) => {
                console.error('❌ Error obteniendo ROSpecs:', error.message);
            });
    },

    // Función para eliminar ROSpecs de forma secuencial (evita problemas de concurrencia)
    eliminarROSpecsSecuencial: function(rospecs, index, callback) {
        if (index >= rospecs.length) {
            // Terminamos de eliminar todos
            callback();
            return;
        }

        const rospec = rospecs[index];
        const rospecId = rospec.getROSpecID();

        console.log('🗑️ Eliminando ROSpec duplicado ID:', rospecId);

        const { LLRPCore } = require('llrpjs');

        // Detener ROSpec
        this.connection.transact(new LLRPCore.STOP_ROSPEC({
            data: { ROSpecID: rospecId }
        }))
        .then(() => {
            // Deshabilitar ROSpec
            return this.connection.transact(new LLRPCore.DISABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
        })
        .then(() => {
            // Eliminar ROSpec
            return this.connection.transact(new LLRPCore.DELETE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
        })
        .then(() => {
            console.log('✅ ROSpec', rospecId, 'eliminado correctamente');
            // Continuar con el siguiente ROSpec
            this.eliminarROSpecsSecuencial(rospecs, index + 1, callback);
        })
        .catch((error) => {
            console.log('⚠️ Error eliminando ROSpec', rospecId, ':', error.message);
            // Continuar con el siguiente aunque haya error
            this.eliminarROSpecsSecuencial(rospecs, index + 1, callback);
        });
    },

    // Función para crear ROSpec optimizado para lectura continua de larga duración
    crearROSpecOptimizado: function() {
        console.log('🆕 CREANDO ROSpec optimizado para lectura continua...');

        if (!this.connection) {
            console.log('❌ No hay conexión activa');
            return;
        }

        const { LLRPCore } = require('llrpjs');

        // Primero eliminar todos los ROSpecs existentes
        console.log('🗑️ Eliminando ROSpecs existentes...');

        this.connection.transact(new LLRPCore.GET_ROSPECS())
            .then((response) => {
                // Procesar ROSpecs existentes
                let rospecs = response.getROSpec ? response.getROSpec() : [];
                if (!Array.isArray(rospecs)) {
                    rospecs = rospecs ? [rospecs] : [];
                }

                if (rospecs.length === 0) {
                    console.log('✅ No hay ROSpecs existentes');
                    this.crearNuevoROSpecOptimizado();
                    return;
                }

                console.log('🗑️ Eliminando', rospecs.length, 'ROSpecs existentes...');

                // Eliminar todos los ROSpecs existentes
                this.eliminarTodosROSpecs(rospecs, 0, () => {
                    console.log('✅ Todos los ROSpecs eliminados');
                    this.crearNuevoROSpecOptimizado();
                });
            })
            .catch((error) => {
                console.error('❌ Error obteniendo ROSpecs:', error.message);
                // Intentar crear el ROSpec de todas formas
                this.crearNuevoROSpecOptimizado();
            });
    },

    // Función para eliminar todos los ROSpecs
    eliminarTodosROSpecs: function(rospecs, index, callback) {
        if (index >= rospecs.length) {
            callback();
            return;
        }

        const rospec = rospecs[index];
        const rospecId = rospec.getROSpecID();

        console.log('🗑️ Eliminando ROSpec ID:', rospecId);

        const { LLRPCore } = require('llrpjs');

        // Secuencia: STOP -> DISABLE -> DELETE
        this.connection.transact(new LLRPCore.STOP_ROSPEC({
            data: { ROSpecID: rospecId }
        }))
        .then(() => {
            return this.connection.transact(new LLRPCore.DISABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
        })
        .then(() => {
            return this.connection.transact(new LLRPCore.DELETE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
        })
        .then(() => {
            console.log('✅ ROSpec', rospecId, 'eliminado');
            this.eliminarTodosROSpecs(rospecs, index + 1, callback);
        })
        .catch((error) => {
            console.log('⚠️ Error eliminando ROSpec', rospecId, ':', error.message);
            this.eliminarTodosROSpecs(rospecs, index + 1, callback);
        });
    },

    // Función para crear el nuevo ROSpec optimizado
    crearNuevoROSpecOptimizado: function() {
        console.log('🆕 Creando ROSpec optimizado...');

        const { LLRPCore } = require('llrpjs');

        // Obtener antenas configuradas
        const antenasHabilitadas = this.obtenerAntenasHabilitadas();
        const rospecId = 999; // ID fijo para el ROSpec optimizado

        console.log('📡 Antenas a usar:', antenasHabilitadas);
        console.log('🆔 ROSpec ID:', rospecId);

        // Configuración ROSpec optimizada para lectura continua
        const rospecConfig = {
            data: {
                ROSpec: {
                    ROSpecID: rospecId,
                    Priority: 0,
                    CurrentState: "Disabled",
                    ROBoundarySpec: {
                        ROSpecStartTrigger: {
                            ROSpecStartTriggerType: "Immediate"
                        },
                        ROSpecStopTrigger: {
                            ROSpecStopTriggerType: "Null" // Lectura continua sin límite de tiempo
                        }
                    },
                    AISpec: {
                        AntennaIDs: antenasHabilitadas,
                        AISpecStopTrigger: {
                            AISpecStopTriggerType: "Null" // Sin límite de tiempo
                        },
                        InventoryParameterSpec: {
                            InventoryParameterSpecID: 1,
                            ProtocolID: "EPCGlobalClass1Gen2"
                        }
                    },
                    ROReportSpec: {
                        ROReportTrigger: "Upon_N_Tags_Or_End_Of_AISpec",
                        N: 1, // Reportar cada tag inmediatamente
                        TagReportContentSelector: {
                            EnableROSpecID: false,
                            EnableSpecIndex: false,
                            EnableInventoryParameterSpecID: false,
                            EnableAntennaID: true,
                            EnableChannelIndex: false,
                            EnablePeakRSSI: true,
                            EnableFirstSeenTimestamp: true,
                            EnableLastSeenTimestamp: false,
                            EnableTagSeenCount: false,
                            EnableAccessSpecID: false
                        }
                    }
                }
            }
        };

        // Crear el ROSpec
        this.connection.transact(new LLRPCore.ADD_ROSPEC(rospecConfig))
            .then(() => {
                console.log('✅ ROSpec creado exitosamente');

                // Habilitar ROSpec
                return this.connection.transact(new LLRPCore.ENABLE_ROSPEC({
                    data: { ROSpecID: rospecId }
                }));
            })
            .then(() => {
                console.log('✅ ROSpec habilitado');

                // Iniciar ROSpec
                return this.connection.transact(new LLRPCore.START_ROSPEC({
                    data: { ROSpecID: rospecId }
                }));
            })
            .then(() => {
                console.log('✅ ROSpec iniciado');

                // Configurar estado interno
                this.rospecActivo = true;
                this.rospecId = rospecId;

                // Finalizar inicialización
                this.finalizarInicializacion();

                console.log('🎉 ROSpec optimizado listo para lectura continua');
                console.log('📡 Esperando tags en antenas:', antenasHabilitadas.join(', '));
                console.log('⏰ Configurado para funcionar 24/7 sin timeouts');

            })
            .catch((error) => {
                console.error('❌ Error creando ROSpec optimizado:', error.message);
            });
    }

};
