
var app = {

    initialize: function () {
        document.addEventListener("deviceready", app.onDeviceReady, false);
        document.addEventListener("DOMContentLoaded", app.onDeviceReady, false);
        document.addEventListener("keydown", app.keydown, false);
    },

    onDeviceReady: function () {
        setInterval(reloj, 87);
        // setInterval(app.reenviar, 60000);
        app.tipoTeclado();
        app.loadLecturas();
        $( "#buscar" ).on( "filterablebeforefilter", function ( e, data ) {
            app.buscar(data.input.val())
        });
        app.control();
    },

    loadLecturas: function () {
        // Limpio el listado
        $("#listaParticipantes").html("");

        // Si no tengo codigo cancelo la carga de los cronos
        if (!validarCodigo(codigo)) {
            alerta('ERROR EN CONFIGURACIÓN');
            return false;
        }

        var cronos = findLocalItems(codigo);

        var ultimo_idcrono = 0;
        for (i = 0; i < cronos.length; i++) {
            var datos = cronos[i];

            // var datos = JSON.parse(crono);
            if (ultimo_idcrono < datos.idcrono)
                ultimo_idcrono = datos.idcrono;

            var lectura = $("#"+datos.idcrono);
            if (!lectura.length) {
                $("#listaParticipantes").prepend(renderTemplate('li', datos));
                var lectura = $("#listaParticipantes li").first();
            }
            switch (datos.a) {
                case 'pena': lectura.html(renderTemplate('pena', datos)); break;
                case 'mod':
                case 'lectura': lectura.html(renderTemplate('lectura', datos)); break;
                case 're-enviar':
                case 'crono':
                case 'error-mod':
                case 'error-baja':
                case 'error': lectura.html(renderTemplate('error', datos)); break;
                case 'esperando': lectura.html(renderTemplate('esperando', datos)); break;
                case 'baja': lectura.html(renderTemplate('baja', datos)); break;
                default: lectura.html(renderTemplate('default', {json: JSON.stringify(datos)})); break;
            }

            actualizarView();
        }

    },

    tipoTeclado: function () {

        if (window.tipo == 'penas') {

            var opciones_penas = window.limitar_penas.replace(" ", "").split(",");
            var datos = {"opciones_penas": []};
            for (i = 0; i < opciones_penas.length; i++)
                datos.opciones_penas.push({"opcion_pena": opciones_penas[i]});
            $("#penas").html(renderTemplate('penas', datos));

            $("#consola").hide();
            $("#totales").hide();
            $("#crono-keys").show();
            $("#crono").hide();
            $("#pena").show();
            $("#penas").show();
            $("#multicrono").hide();
            $("#reloj").parent().hide();
            $("#tiempo_pena").parent().show();

            $("#buscador-btn").hide();
            $("#qr").hide();
            $("#esperar").hide();

        } else {

            $("#consola").hide();
            $("#totales").hide();
            $("#crono-keys").show();
            $("#crono").show();
            $("#pena").hide();
            $("#penas").hide();
            $("#multicrono").hide();
            $("#reloj").parent().show();
            $("#tiempo_pena").parent().hide();

            $("#buscador-btn").show();
            $("#qr").show();
            $("#esperar").show();

        }

        $("#listaParticipantes").prepend(renderTemplate('li', datos));
        var lectura = $("#listaParticipantes li").first();

        switch (window.tipo) {
            default: var tipo = 'Cronometraje'; break;
            case "largada": var tipo = "Largada"; break;
            case "final": var tipo = "Llegada"; break;
            case "etapa": var tipo = "Cambio de etapa"; break;
            case "control": var tipo = "Puesto de control"; break;
            case "parcial": var tipo = "Tiempo parcial"; break;
            case "penas": var tipo = "Puesto de penas y bonus"; break;
            case "pre-largada": var tipo = "Pre largada"; break;
            case "pre-llegada": var tipo = "Pre llegada"; break;
            case "acreditar": var tipo = "Acreditar"; break;
        }

        lectura.html(renderTemplate('control', {
            'evento': window.evento,
            'codigo': window.codigo,
            'nombre': window.nombre,
            'tipo': tipo
        }));

    },

    teclado: function (tecla) {
        var idparticipante = $("#idparticipante").html();
        switch (tecla) {
            default: // Números
                $("#idparticipante").html((idparticipante != '-'
                    ? idparticipante
                    : '')
                    + tecla
                );
                break;

            case 'backspace':
                $("#idparticipante").html(idparticipante.length == 1
                    ? '-'
                    : $("#idparticipante").html().substr(0, $("#idparticipante").html().length - 1)
                );
                break;

            case 'crono':
                app.crono();
                break;

            case 'pena':
                app.pena();
                break;

            case 'esperar':
                app.esperar();
                break;

            case 'multicrono':
                app.multicrono();
                break;

            case 'reenviar':
                app.reenviar();
                break;

            case 'config':
                app.config();
                break;

            case 'scanqr':
                app.scanQR();
                break;

            case 'buscador':
                app.buscador();
                break;

            };

    },

    keydown: function (e) {

        // Se está usando el buscador
        if ($(".ui-filterable input").is(":focus"))
            return true;

        // The popup is open
        if ($(".ui-page-active .ui-popup-active").length > 0)
            return true;

        var charCode = (e.which) ? e.which : e.keyCode;
        if ((charCode >= 48 && charCode <= 57) || (charCode >= 96 && charCode <= 105)) {
            // Con keydown los números del padnum necesitan convertirse para que funcione el fromCharCode
            if (charCode >= 96 && charCode <= 105)
                charCode = charCode - 48;

            e.preventDefault();
            var idparticipante = $("#idparticipante").html();
            $("#idparticipante").html(
                (idparticipante != '-' ? idparticipante : '')
                    + String.fromCharCode(charCode));

        } else if (charCode == 8) {

            e.preventDefault();
            var idparticipante = $("#idparticipante").html();
            $("#idparticipante").html(
                idparticipante.length == 1
                    ? '-'
                    : idparticipante.substr(0, idparticipante.length - 1));

        } else if (charCode == 13 && enter) {

            if (enter == 'esperar') {
                app.esperar();

            } else {
                app.crono();
            }

        } else {
          // console.log(charCode);
        }

    },

    crono: function (id = null, date_crono = null) {
        console.log('🚨 [CRITICAL] app.crono() iniciado');

        if (typeof id === typeof null)
            var idparticipante = $("#idparticipante").html();
        else
            var idparticipante = id;
        var tagID = $("#tagID").html();
        var date = date_crono ? date_crono : getDate();

        console.log('🚨 [CRITICAL] Datos obtenidos:', {idparticipante, tagID, date});

        var datos = {
            idcrono: window.idcrono,
            a: 'crono',
            idparticipante: idparticipante,
            tiempo: date.tiempo,
            hora: date.hora,
            codigo: window.codigo,
            cronometrador: window.cronometrador,
            tagID: tagID
        };
        console.log('🚨 [CRITICAL] Llamando plusIdCrono()');
        plusIdCrono();

        console.log('🚨 [CRITICAL] Agregando a lista participantes');
        $("#listaParticipantes").prepend(renderTemplate('li', datos));
        var lectura = $("#listaParticipantes li").first();

        console.log('🚨 [CRITICAL] Iniciando AJAX request');
        var request = $.ajax({
            url: url_api + "lectura.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                logs(datos);
                lectura.html(renderTemplate('cargando', datos));
                lectura.find(".accion").html('<img src="images/ajax-loader.gif"> Conectando');
            },
            success: function (data) {
                datos.a = 'lectura';
                datos.idlectura = data.idlectura;
                datos.resultado = data.resultado;
                datos.tiempo = data.tiempo;
                datos.extra = data.extra;
                datos.hora = data.hora;
                datos.tiempo_listo = data.tiempo_listo;
                datos.idparticipante = data.idparticipante;
                datos.nombre = data.nombre;

                logs(datos);
                lectura.html(renderTemplate('lectura', datos)).promise().done(actualizarView);
            },
            error: function (data) {
                // alerta(JSON.stringify(data))
                datos.a = 'error';
                datos.resultado = typeof data.responseJSON !== typeof undefined
                    && data.responseJSON.resultado
                    ? data.responseJSON.resultado
                    : 'RE-ENVIAR';
                logs(datos);
                lectura.html(renderTemplate('error', datos)).promise().done(actualizarView);
            }
        });
        console.log('🚨 [CRITICAL] AJAX configurado, limpiando campos');
        $("#idparticipante").html('-');
        $("#tagID").html('');
        console.log('🚨 [CRITICAL] Agregando a requests array');
        requests.push({'idcrono': datos.idcrono, 'request': request});
        console.log('🚨 [CRITICAL] app.crono() COMPLETADO');
    },

    esperar: function () {
        var idparticipante = $("#idparticipante").html();
        var tagID = $("#tagID").html();

        var datos = {
            idcrono: window.idcrono,
            a: 'esperando',
            idparticipante: idparticipante,
            tagID: tagID,
            resultado: 'Esperando'
        };
        plusIdCrono();

        logs(datos);
        $("#listaParticipantes").prepend(renderTemplate('li', datos));
        var lectura = $("#listaParticipantes li").first();
        $("#idparticipante").html('-');
        $("#tagID").html('');
        lectura.html(renderTemplate('esperando', datos)).promise().done(actualizarView);
    },

    esperando: function (that, date_esperando = null) {
        var lectura = that.parent();
        var idcrono = lectura.find(".idcrono").html();
        var idparticipante = lectura.find(".idparticipante").html();
        var date = date_esperando ? date_esperando : getDate();

        var datos = {
            idcrono: idcrono,
            a: 'crono-esperando',
            idparticipante: idparticipante,
            tiempo: date.tiempo,
            hora: date.hora,
            codigo: codigo,
            cronometrador: window.cronometrador
        }

        var request = $.ajax({
            url: url_api + "lectura.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                logs(datos);
                lectura.html(renderTemplate('cargando', datos));
                lectura.find(".accion").html('<img src="images/ajax-loader.gif"> Conectando');
            },
            success: function (data) {
                datos.a = 'lectura';
                datos.idlectura = data.idlectura;
                datos.resultado = data.resultado;
                datos.tiempo = data.tiempo;
                datos.extra = data.extra;
                datos.hora = data.hora;
                datos.tiempo_listo = data.tiempo_listo;
                datos.idparticipante = data.idparticipante;
                datos.nombre = data.nombre;

                logs(datos);
                lectura.html(renderTemplate('lectura', datos)).promise().done(actualizarView);
            },
            error: function (data) {
                datos.a = 'error';
                datos.resultado = 'RE-ENVIAR';
                logs(datos);
                lectura.html(renderTemplate('error', datos)).promise().done(actualizarView);
            }
        });
        requests.push({'idcrono': datos.idcrono, 'request': request});

    },

    multicrono: function () {
        var lecturas = $(".esperando");
        if (!lecturas.length) {
            alerta('Nada para multicrono');
        }

        var date_esperando = getDate();

        lecturas.each(function () {
            app.esperando($(this), date_esperando);
        });

        $("#multicrono").hide();

    },

    reenviar: function () {
        var lecturas = $(".re-enviar").last();
        if (!lecturas.length)
            $("#reenviar-btn").css('background-color', 'grey');

        lecturas.each(function () {
            var lectura = $(this).parent();
            var idparticipante = lectura.find(".idparticipante").html();
            var tagID = lectura.find(".tagID").html();
            var tiempo = lectura.find(".tiempo").html();
            var hora = lectura.find(".hora").html();
            var idcrono = lectura.find(".idcrono").html();

            var datos = {
                idcrono: idcrono,
                a: 're-enviar',
                idparticipante: idparticipante,
                tagID: tagID,
                hora: hora,
                tiempo: tiempo,
                codigo: codigo,
                cronometrador: window.cronometrador
            }

            var request = $.ajax({
                url: url_api + "lectura.php",
                type:"POST",
                dataType: "json",
                data: datos,
                beforeSend: function () {
                    logs(datos);
                    lectura.html(renderTemplate('cargando', datos));
                    lectura.find(".accion").html('<img src="images/ajax-loader.gif"> Re-enviando');
                },
                success: function (data) {
                    datos.a = 'lectura';
                    datos.idlectura = data.idlectura;
                    datos.resultado = data.resultado;
                    datos.tiempo = data.tiempo;
                    datos.extra = data.extra;
                    datos.hora = data.hora;
                    datos.tiempo_listo = data.tiempo_listo;
                    datos.idparticipante = data.idparticipante;
                    datos.nombre = data.nombre;

                    logs(datos);
                        lectura.html(renderTemplate('lectura', datos)).promise().done(actualizarView);

                    app.reenviar();
                },
                error: function (data) {
                    datos.a = 'error';
                    datos.resultado = typeof data.responseJSON !== typeof undefined
                        && data.responseJSON.resultado
                        ? data.responseJSON.resultado
                        : 'RE-ENVIAR';
                    datos.accion = 'NO SE PUDO RE-ENVIAR';
                    logs(datos);
                        lectura.html(renderTemplate('error', datos)).promise().done(actualizarView);
                }
            });
            requests.push({'idcrono': datos.idcrono, 'request': request});
        });
    },

    cancel: function (that) {
        var lectura = that.parent();
        var idcrono = lectura.find(".idcrono").html();

        for (var i in requests) {
            if (requests[i].idcrono == idcrono)
                requests[i].request.abort();
        }
    },

    config: function () {
        popupOpen('configurar', {
            compatible_hardware: (tipo != 'qr'),
            compatible_nube_hibrida: (tipo != 'qr'),
            codigo: codigo,
            cronometrador: cronometrador,
            nube_hibrida: (nube_hibrida == true ? 'checked' : ''),
//            conectar_hardware: (conectar_hardware == true ? 'checked' : ''),
            conectar_hardware: (typeof hardware.conexiones[0] != 'undefined' && typeof hardware.conexiones[0].nombre != 'undefined')
                ? hardware.conexiones[0].nombre : '',
            version: VERSION
        });
    },

    configurar: function () {
        var configurarForm = $('#configurarForm');
        var codigo_temp = configurarForm.find("input[name=codigo]").val().toUpperCase();

        window.cronometrador = configurarForm.find("input[name=cronometrador]").val();
        setConfig('cronometrador', window.cronometrador);

        setConfig('conectar_hardware',
            configurarForm.find("input[name=conectar_hardware]").is(':checked')
                ? true
                : false);

        setConfig('nube_hibrida',
            (configurarForm.find("input[name=nube_hibrida]").is(':checked')
                && confirm('¿Está seguro que desea conectarse a una nube híbrida?'))
                ? true
                : false);

        if (PLATFORM == 'windows') {
            url_anterior = url_api;
            url_api = nube_hibrida ? URL_API_LOCAL : URL_API_ONLINE;

        } else {
            let url_anterior = url_api;
            let url_sugerida = (url_api != URL_API_LOCAL && url_api != URL_API_ONLINE)
                ? url_api
                : URL_API_LOCAL;
            url_api = nube_hibrida
                ? window.prompt('Dirección de Nube', url_sugerida)
                : URL_API_ONLINE;
        }
        setConfig('url_api', url_api);

        if (codigo_temp == 'CON') {
            // $("#crono-keys").hide();
            $("#consola").show();
        } else if (codigo_temp == 'MIL') {
            test(1000);
        } else if (codigo_temp == 'LIM') {
            if (confirm("¡CUIDADO!,\n¿Está seguro que desea vaciar todos los datos?")) {
                localStorage.clear();
                app.loadLecturas();
            }
        } else if (!validarCodigo(codigo_temp)) {
            alerta('Código de control no válido');

        } else if (validarCodigo(codigo_temp)
            && (codigo_temp != window.codigo || url_anterior != url_api)) {
            app.control(codigo_temp);
        }

        popupClose();
    },

    configHardware: function()
    {
        hardware.initialize();
        popupOpen('hardware', {
            "windows": (PLATFORM == 'windows' ? true : false),
            "linux": (PLATFORM == 'linux' ? true : false),
            "android": (PLATFORM == 'android' ? true : false),
        });
        // TODO convertir el diseño a multi-dispositivo
        if (typeof hardwares[0] === "object" && hardwares[0] !== null) {
            $("#hardwareForm select[name=dispositivo1] option[value='" + hardwares[0].dispositivo + "']").prop("selected", true);
            $("#hardwareForm select[name=puerto1] option[value='" + hardwares[0].puerto + "']").prop("selected", true);
        }

        if (PLATFORM == 'windows') {
            $("#hardwareForm select[name=rfid_crono] option[value='" + window.rfid_crono + "']").prop("selected", true);
            $("#hardwareForm input[name=sonido]").prop("checked", window.sonido);
            $("#hardwareForm select[name=rebote] option[value='" + window.rebote + "']").prop("selected", true);
            // $("#hardwareForm select[name=intervalo] option[value='" + window.intervalo + "']").prop("selected", true);
            $("#hardwareForm input[name=tag_prefijo]").val(window.tag_prefijo);
            $("#hardwareForm input[name=antena_1]").prop("checked", window.antena_1);
            $("#hardwareForm input[name=antena_2]").prop("checked", window.antena_2);
            $("#hardwareForm input[name=antena_3]").prop("checked", window.antena_3);
            $("#hardwareForm input[name=antena_4]").prop("checked", window.antena_4);
            $("#hardwareForm input[name=antena_5]").prop("checked", window.antena_5);
            $("#hardwareForm input[name=antena_6]").prop("checked", window.antena_6);
            $("#hardwareForm input[name=antena_7]").prop("checked", window.antena_7);
            $("#hardwareForm input[name=antena_8]").prop("checked", window.antena_8);
            isTagCodigo();
        }
    },

    control: function(codigo = null) {

        codigo = codigo ?? window.codigo;

        $.ajax({
            url: url_api + "control.php",
            type: "POST",
            dataType: "json",
            data: {
                codigo: codigo
            },
            success: function (data) {
                setConfig('codigo', codigo);
                setConfig('tipo', data.tipo);
                setConfig('evento', data.evento);
                setConfig('nombre', data.nombre);
                setConfig('limitar_penas', data.limitar_penas);
                window.idcrono = getIdCrono();
                app.loadLecturas();
                app.tipoTeclado();
            },
            error: function (data) {
                if (typeof data.responseJSON != 'undefined' && typeof data.responseJSON.error != 'undefined') {
                    if (typeof data.responseJSON != 'undefined' && typeof data.responseJSON.resultado != 'undefined')
                        alert(data.responseJSON.resultado);
                    else
                        alert('El código no existe');
                    return false;
                }

                alert('No se pudo verificar el código. Asegurese que ' + codigo + ' sea el correcto antes de cronometrar');

                setConfig('codigo', codigo);
                setConfig('tipo', 'PC');
                setConfig('evento', 'SIN VERIFICAR');
                setConfig('nombre', '');
                window.idcrono = getIdCrono();
                app.loadLecturas();
                app.tipoTeclado();
            }
        });
    },

    hardware: function () {
        try {
            hardware.configurar();
        } catch (error) {
            alerta(error);
        }
        popupClose();
    },

    mod: function (that) {
        var lectura = that.parent();
        var idcrono = lectura.find(".idcrono").html();
        var idparticipante = lectura.find(".idparticipante").html();
        var tagID = lectura.find(".tagID").html();
        var idlectura = lectura.find(".idlectura").html();
        var resultado = lectura.find(".resultado").html();
        var tiempo = lectura.find(".tiempo").html();
        var hora = lectura.find(".hora").html();
        var tiempo_listo = lectura.find(".tiempo_listo").html();
        var nombre = lectura.find(".nombre").val();

        popupOpen('edit', {
            idcrono: idcrono,
            idlectura: idlectura,
            idparticipante: idparticipante,
            tagID: tagID,
            resultado: resultado,
            tiempo: tiempo,
            hora: hora,
            hora_1: hora.split('.')[0].split(':')[0],
            hora_2: hora.split('.')[0].split(':')[1],
            hora_3: hora.split('.')[0].split(':')[2],
            hora_4: hora.split('.')[1],
            tiempo_listo: tiempo_listo
        });

    },

    ok: function () {
        var edit = $("#editForm");
        var idcrono = edit.find("input[name='idcrono']").val();
        var idlectura = edit.find("input[name='idlectura']").val();
        var idparticipante = edit.find("input[name='idparticipante']").val();
        var tiempo = edit.find("input[name='tiempo']").val();
        var resultado = edit.find("input[name='resultado']").val();
        var hora = edit.find("input[name='hora']").val();
        var tiempo_listo = edit.find("input[name='tiempo_listo']").val();

        var datos = {
            idcrono: idcrono,
            a: 'enviar-mod',
            idlectura: idlectura,
            idparticipante: idparticipante,
            tiempo: tiempo,
            resultado: resultado,
            hora: hora,
            tiempo_listo: tiempo_listo,
            codigo: codigo
        };

        if (idlectura == '') {
            $("#"+idcrono).find(".idparticipante").html(idparticipante);
            $("#"+idcrono).find(".hora").html(hora);
            datos.a = 'error-mod';
            logs(datos);
            popupClose();
            $("#"+idcrono).html(renderTemplate('error', datos));
            return true;
        }

        $.ajax({
            url: url_api + "mod.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                $("#"+idlectura).parent().find(".accion").html('<img src="images/ajax-loader.gif"> Modificando');
                logs(datos);
                popupClose();
            },
            success: function (data) {
                datos.a = 'mod';
                datos.idlectura = data.idlectura;
                datos.resultado = data.resultado;
                datos.accion = 'Modificado';
                datos.tiempo = data.tiempo;
                datos.extra = data.extra;
                datos.hora = data.hora;
                datos.tiempo_listo = data.tiempo_listo;
                datos.idparticipante = data.idparticipante;
                datos.nombre = data.nombre;

                logs(datos);
                $("#"+idlectura).parent().html(renderTemplate('lectura', datos));
            },
            error: function (data) {
                datos.a = 'error-mod';
                datos.accion = 'NO SE PUDO MODIFICAR';
                logs(datos);
                $("#"+idlectura).parent().html(renderTemplate('lectura', datos));
            }
        });
    },

    baja: function () {
        if (confirm("¿Borrar?")) {
            var edit = $("#editForm");
            var idcrono = edit.find("input[name='idcrono']").val();
            var idlectura = edit.find("input[name='idlectura']").val();
            var idparticipante = edit.find("input[name='idparticipante']").val();
            var tiempo = edit.find("input[name='tiempo']").val();
            var resultado = edit.find("input[name='resultado']").val();
            var hora = edit.find("input[name='hora']").val();
            var tiempo_listo = edit.find("input[name='tiempo_listo']").val();
            var nombre = edit.find(".nombre").val();

            var datos = {
                idcrono: idcrono,
                a: 'enviar-baja',
                codigo: codigo,
                idlectura: idlectura,
                idparticipante: idparticipante,
                tiempo: tiempo,
                resultado: resultado,
                hora: hora,
                tiempo_listo: tiempo_listo,
                nombre: nombre,
                codigo: codigo
            };

            if (idcrono && !idlectura) {
                popupClose();
                datos.a = 'baja';
                datos.accion = 'Borrado';
                logs(datos);
                $("#"+idcrono).html(renderTemplate('baja', datos));
                return true;
            }

            $.ajax(
            {
                url: url_api + "baja.php",
                type:"POST",
                data: datos,
                beforeSend: function () {
                    $("#"+idlectura).parent().find(".accion").html('<img src="images/ajax-loader.gif"> Borrando');
                    logs(datos);
                    popupClose();
                },
                success: function (data) {
                    datos.a = 'baja';
                    datos.accion = 'Borrado';
                    logs(datos);
                    $("#"+idlectura).parent().html(renderTemplate('baja', datos));
                },
                error: function (data) {
                    datos.a = 'error-baja';
                    datos.accion = 'NO SE PUDO BORRAR';
                    logs(datos);
                    $("#"+idlectura).parent().html(renderTemplate('lectura', datos));
                }
            });
        }
    },

    star: function (that) {
        var lectura = that.parent();
        var resaltar = that.find(".resaltar").html();
        if (resaltar == 'ui-icon-star') {
            var nuevoresaltar = 'ui-icon-nostar';
        } else {
            var nuevoresaltar = 'ui-icon-star';
        }
        that.removeClass(resaltar);
        that.addClass(nuevoresaltar);
        lectura.find(".resaltar").html(nuevoresaltar);
    },

    menos: function () {
        var edit = $("#editForm");
        var inputTiempo = edit.find("input[name='tiempo']");
        var inputTiempoListo = edit.find("input[name='tiempo_listo']");
        var inputTiempoListoOriginal = edit.find("input[name='tiempo_listo_original']");
        var inputHora_1 = edit.find("input[name='hora_1']");
        var inputHora_2 = edit.find("input[name='hora_2']");
        var inputHora_3 = edit.find("input[name='hora_3']");
        var inputHora_4 = edit.find("input[name='hora_4']");

        var nuevoTiempo = unixToTiempo( tiempoToUnix(inputTiempo.val()) - 1000 );
        var nuevoTiempoListo = unixToTiempo( tiempoToUnix(inputTiempoListo.val()) - 1000 );

        inputTiempo.val(nuevoTiempo.tiempo);
        inputTiempoListo.val(nuevoTiempoListo.hora);
        inputTiempoListoOriginal.val(nuevoTiempoListo.hora);

        inputHora_1.val(nuevoTiempo.hora.split('.')[0].split(':')[0]);
        inputHora_2.val(nuevoTiempo.hora.split('.')[0].split(':')[1]);
        inputHora_3.val(nuevoTiempo.hora.split('.')[0].split(':')[2]);
        inputHora_4.val(nuevoTiempo.hora.split('.')[1]);
    },

    mas: function () {
        var edit = $("#editForm");
        var inputTiempo = edit.find("input[name='tiempo']");
        var inputTiempoListo = edit.find("input[name='tiempo_listo']");
        var inputTiempoListoOriginal = edit.find("input[name='tiempo_listo_original']");
        var inputHora_1 = edit.find("input[name='hora_1']");
        var inputHora_2 = edit.find("input[name='hora_2']");
        var inputHora_3 = edit.find("input[name='hora_3']");
        var inputHora_4 = edit.find("input[name='hora_4']");

        var nuevoTiempo = unixToTiempo( tiempoToUnix(inputTiempo.val()) + 1000 );
        var nuevoTiempoListo = unixToTiempo( tiempoToUnix(inputTiempoListo.val()) + 1000 );

        inputTiempo.val(nuevoTiempo.tiempo);
        inputTiempoListo.val(nuevoTiempoListo.hora);
        inputTiempoListoOriginal.val(nuevoTiempoListo.hora);

        inputHora_1.val(nuevoTiempo.hora.split('.')[0].split(':')[0]);
        inputHora_2.val(nuevoTiempo.hora.split('.')[0].split(':')[1]);
        inputHora_3.val(nuevoTiempo.hora.split('.')[0].split(':')[2]);
        inputHora_4.val(nuevoTiempo.hora.split('.')[1]);
    },

    changeHora: function () {
        var edit = $("#editForm");
        var inputTiempo = edit.find("input[name='tiempo']");
        var tempTiempo = inputTiempo.val().split(" ");
        var inputHora1 = edit.find("input[name='hora_1']").val();
        var inputHora2 = edit.find("input[name='hora_2']").val();
        var inputHora3 = edit.find("input[name='hora_3']").val();
        var inputHora4 = edit.find("input[name='hora_4']").val();
        var inputHora = inputHora1 + ':' + inputHora2 + ':' + inputHora3 + '.' + inputHora4;

        var inputTiempoListo = edit.find("input[name='tiempo_listo']");
        var inputTiempoListoOriginal = edit.find("input[name='tiempo_listo_original']");

        var idlectura = edit.find("input[name='idlectura']").val();

        // var validarHora = inputHora.val().split(":");

        if (true) { //validarHora.length == 3) {
            var unixTiempo = tiempoToUnix(inputTiempo.val());
            var unixHora = tiempoToUnix(tempTiempo[0] + " " + inputHora);
            var diferencia = unixHora - unixTiempo;

            var nuevoTiempo = unixToTiempo(unixTiempo + diferencia);
            inputTiempo.val(nuevoTiempo.tiempo);
            edit.find("input[name='hora']").val(nuevoTiempo.hora);

            if (idlectura == '') // Estoy editando un error para re-enviar
                return true;

            var nuevoTiempoListo = unixToTiempo( tiempoToUnix(inputTiempoListo.val()) + diferencia );
            inputTiempoListo.val(nuevoTiempoListo.hora);
            inputTiempoListoOriginal.val(nuevoTiempoListo.hora);
        }
    },

    changeTiempo: function () {
        var edit = $("#editForm");
        var inputTiempo = edit.find("input[name='tiempo']");
        var tempTiempo = inputTiempo.val().split(" ");
        var inputHora = edit.find("input[name='hora']");
        var inputTiempoListo = edit.find("input[name='tiempo_listo']");
        var inputTiempoListoOriginal = edit.find("input[name='tiempo_listo_original']");

        var validarTiempoListo = inputTiempoListo.val().split(":");

        if (validarTiempoListo.length == 3) {
            var unixTiempoListoOriginal = tiempoToUnix(inputTiempoListoOriginal.val());
            var unixTiempoListo = tiempoToUnix(tempTiempo[0] + " " + inputTiempoListo.val());
            var diferencia = unixTiempoListo - unixTiempoListoOriginal;

            var unixTiempo = tiempoToUnix( inputTiempo.val() );
            var nuevoHora = unixToTiempo( tiempoToUnix(inputHora.val()) + diferencia );
            var nuevoTiempo = unixToTiempo( unixTiempo + diferencia );
            var nuevoTiempoListo = unixToTiempo( unixTiempoListo );

            inputTiempo.val(nuevoTiempo.tiempo);
            inputHora.val(nuevoTiempo.hora);
            inputTiempoListo.val(nuevoTiempoListo.hora);
            inputTiempoListoOriginal.val(nuevoTiempoListo.hora);
        }
    },

    keyupHora: function () {
        var input_hora = $("input:focus");

        if (input_hora.attr('name') == 'hora_1' && input_hora.val().length > 1)
            delay(() => { $("input[name=hora_2]").focus().select() }, 50);
        else if (input_hora.attr('name') == 'hora_2' && input_hora.val().length > 1)
            delay(() => { $("input[name=hora_3]").focus().select() }, 50);
        else if (input_hora.attr('name') == 'hora_3' && input_hora.val().length > 1)
            delay(() => { $("input[name=hora_4]").focus().select() }, 50);
        else if (input_hora.attr('name') == 'hora_4' && input_hora.val().length > 2)
            input_hora.blur();

    },

    opcionPena: function (opcion_pena) {
        var tiempo_pena = $("#tiempo_pena").html(opcion_pena);
    },

    pena: function () {
        var idparticipante = $("#idparticipante").html();
        var tiempo_pena = $("#tiempo_pena").html();

        var datos = {
            idcrono: window.idcrono,
            a: 'pena',
            idparticipante: idparticipante,
            codigo: window.codigo,
            tiempo: tiempo_pena
        };
        plusIdCrono();

        $("#listaParticipantes").prepend(renderTemplate('li', datos));
        var lectura = $("#listaParticipantes li").first();

        var request = $.ajax({
            url: URL_API_ADMIN + "penas",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                logs(datos);
                lectura.html(renderTemplate('cargando', datos));
                lectura.find(".accion").html('<img src="images/ajax-loader.gif"> Conectando');
            },
            success: function (data) {
                datos.a = 'pena';
                datos.idlectura = data.idlectura;
                datos.resultado = 'OK';
                datos.tiempo = data.tiempo;
                datos.idcambiotiempo = data.idcambiotiempo;
                datos.idparticipante = data.idparticipante;
                datos.nombre = data.nombre;

                logs(datos);
                lectura.html(renderTemplate('pena', datos));
            },
            error: function (data) {
                alerta('ERROR conectando al servidor');
                // datos.a = 'error';
                // datos.resultado = typeof data.responseJSON !== typeof undefined
                //     && data.responseJSON.resultado
                //     ? data.responseJSON.resultado
                //     : 'RE-ENVIAR';
                // logs(datos);
                // lectura.html(renderTemplate('error', datos));
            }
        });
        $("#idparticipante").html('-');
        $("#tiempo_pena").html('-');
        requests.push({'idcrono': datos.idcrono, 'request': request});
    },

    baja_pena: function () {
        if (confirm("¿Borrar?")) {
            var lectura = this.parent();
            var idcambiotiempo = lectura.find(".idcambiotiempo").html();

            var datos = {
                idcrono: idcrono,
                a: 'enviar-baja',
                codigo: codigo,
                idlectura: idlectura,
                idparticipante: idparticipante,
                tiempo: tiempo,
                resultado: resultado,
                hora: hora,
                tiempo_listo: tiempo_listo,
                nombre: nombre,
                codigo: codigo
            };

            if (idcrono && !idlectura) {
                popupClose();
                datos.a = 'baja';
                datos.accion = 'Borrado';
                logs(datos);
                $("#"+idcrono).html(renderTemplate('baja', datos));
                return true;
            }

            $.ajax(
            {
                url: url_api + "baja.php",
                type:"POST",
                data: datos,
                beforeSend: function () {
                    $("#"+idlectura).parent().find(".accion").html('<img src="images/ajax-loader.gif"> Borrando');
                    logs(datos);
                    popupClose();
                },
                success: function (data) {
                    datos.a = 'baja';
                    datos.accion = 'Borrado';
                    logs(datos);
                    $("#"+idlectura).parent().html(renderTemplate('baja', datos));
                },
                error: function (data) {
                    datos.a = 'error-baja';
                    datos.accion = 'NO SE PUDO BORRAR';
                    logs(datos);
                    $("#"+idlectura).parent().html(renderTemplate('lectura', datos));
                }
            });
        }
    },

    scanQR: function () {

        // app.scanedQR('4892798374');
        // return true;

        cordova.plugins.barcodeScanner.scan(function (result) {
            // if (result.format == "QR_CODE" || result.format == "EAN_8" || result.format == "EAN_13") {
                app.scanedQR(result.text);
            // } else {
            //     alert('Tipo de código no soportado: ' + result.format);
            // }
        },
        function (error) {
            datos.a = 'scan-error';
            datos.resultado = "ERROR AL ESCANEAR QR"
            logs(datos);
        })
    },

    scanedQR: function(scanedQR) {

        // alerta(scanedQR);
        // QR de Crono: https://cronometrajeinstantaneo.com/inscripciones/verano-2021/138897?idparticipante=28&nombre=Andres Misiak&h=1500380ee907ca74674a3f774896cab3326cec5c5cec59b97186a82639e3f3a9
        if (scanedQR.includes('cronometrajeinstantaneo.com')) {
            let split = scanedQR.split("?");
            let url = split[0].split('/');
            let parametros = split[1].split('&')

            let codigo = url[url.length - 2];
            let idinscripcion = url[url.length - 1];

            let idparticipante = parametros[0].split('=')[1];
            // let nombre = parametros[1].split('=')[1];
            // let hash = parametros[2].split('=')[1];

            if (app.validateHash(codigo, idinscripcion)) {
                $("#idparticipante").html(idparticipante);
                app.crono();
            }

        // DNI Argentina: 00576910250@MISIAK@ANDRES@M@18787464@C@16/12/1979@02/01/2019@203
        } else if ((scanedQR.match(/@/g) || []).length > 6) {

            let datos = scanedQR.split('@');
            let dni = datos[4];
            let buscador = $("#buscador");
            buscador.show();
            buscador.val(dni);
            buscador.find("input").val(dni)
            app.buscar(dni);

        // Cédula Chile: https://portal.sidiv.registrocivil.cl/docstatus?RUN=16488088-5&type=CEDULA&serial=526262968&mrz=59872309487294892798374
        } else if (scanedQR.includes('portal.sidiv.registrocivil.cl')) {

            let split = scanedQR.split("?");
            let parametros = split[1].split('&')
            let run = parametros[0].split('=')[1];

            let buscador = $("#buscador");
            buscador.show();
            buscador.val(run);
            buscador.find("input").val(run)
            app.buscar(run);

        // Cédula Ecuador Vieja: 1002392460
        } else if (!isNaN(scanedQR)) {

            let buscador = $("#buscador");
            buscador.show();
            buscador.val(scanedQR);
            buscador.find("input").val(scanedQR)
            app.buscar(scanedQR);

        } else {
            alert('No se reconoce la información en el QR: ' + scanedQR);
        }

    },

    validateHash: function (codigo, idinscripcion) {

        return true;

        // let message = codigo + idinscripcion + HASH_SALT;
        // var digestHex = await digestMessage(message);
    },

    buscador: function () {
        if ($("#buscador").is(":visible"))
            $("#buscador").hide();
        else
            $("#buscador").show();
    },

    buscar: function (buscar) {

        if (window.request_buscando) {
            window.request_busqueda.abort();
        }

        var busqueda = $("#buscar");
        var html = '';
        var datos = {
            codigo: window.codigo,
            buscar: buscar
        };

        busqueda.html( html );
        busqueda.listview( "refresh" );

        if (buscar.length <= 3) {
            return true;
        }

        window.request_busqueda = $.ajax({
            url: url_api + "buscar.php",
            type:"POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                $("#buscando-gif").show();
                window.request_buscando = true;
            },
            success: function (data) {
                $("#buscando-gif").hide();
                if (data.length) {
                    $.each( data, function ( i, val ) {
                        html += '<li><a class="ui-btn ui-btn-icon-right ui-icon-carat-r" href="#" onclick="app.encontrado(' + val.idparticipante + ')">'
                            + val.nombre
                            + (val.extra ? ' - ' + val.extra : '')
                            + '</a></li>';
                    });
                } else {
                    html += '<li><a class="ui-btn ui-btn-icon-right ui-icon-carat-r" href="#">No existe participante</a></li>';
                }
                busqueda.html( html );
                busqueda.trigger( "updatelayout" );
                window.request_buscando = false;
            },
            error: function (data) {
                $("#buscando-gif").hide();
                window.request_buscando = false;
                alerta('Ocurrió un error buscando ' + data);
            }
        });

    },

    encontrado: function (idparticipante = 0) {

        var busqueda = $("#buscar");
        $('input[data-type="search"]').val("");
        busqueda.html( "" );
        busqueda.listview( "refresh" );
        busqueda.trigger( "updatelayout");
        app.crono(idparticipante);

    },

    tag: function(modo) {
        var idparticipante = $("#idparticipante").html();
        var tagID = $("#tagID").html();

        var datos = {
            idcrono: window.idcrono,
            a: modo,
            idparticipante: idparticipante,
            codigo: window.codigo,
            cronometrador: window.cronometrador,
            tagID: tagID,
            tag_prefijo: window.tag_prefijo
        };
        // plusIdCrono();

        $("#listaParticipantes").prepend(renderTemplate('li', datos));
        var lectura = $("#listaParticipantes li").first();

        var request = $.ajax({
            url: url_api + "tag.php",
            type: "POST",
            dataType: "json",
            data: datos,
            beforeSend: function () {
                lectura.html(renderTemplate('cargando', datos));
                lectura.find(".accion").html('<img src="images/ajax-loader.gif"> Conectando');
            },
            success: function (data) {
                datos.a = 'tag';
                datos.resultado = data.resultado;
                datos.idparticipante = data.idparticipante;
                datos.nombre = data.nombre;

                lectura.html(renderTemplate('tag', datos)).promise().done(actualizarView);
            },
            error: function (data) {
                datos.resultado = typeof data.responseJSON !== typeof undefined
                    && data.responseJSON.resultado
                    ? data.responseJSON.resultado
                    : 'Error de conexión al asignar chip';
                lectura.html(renderTemplate('error', datos)).promise().done(actualizarView);
            }
        });
        $("#idparticipante").html('-');
        requests.push({'idcrono': datos.idcrono, 'request': request});

    }

}
