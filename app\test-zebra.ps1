# Script de PowerShell para facilitar el testing del Zebra FX9600
# Uso: .\test-zebra.ps1 [opciones]

param(
    [string]$Host = "",
    [string]$Port = "",
    [string]$Config = "",
    [string]$ROSpec = "",
    [string]$Output = "",
    [switch]$Help,
    [switch]$SingleAntenna,
    [switch]$FastReporting
)

if ($Help) {
    Write-Host "🦓 Zebra FX9600 RFID Reader Tester - Script de PowerShell" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "USO:" -ForegroundColor Yellow
    Write-Host "    .\test-zebra.ps1 [opciones]"
    Write-Host ""
    Write-Host "OPCIONES:" -ForegroundColor Yellow
    Write-Host "    -Host <ip>           IP del reader"
    Write-Host "    -Port <puerto>       Puerto del reader"
    Write-Host "    -Config <archivo>    Archivo de configuración personalizado"
    Write-Host "    -ROSpec <archivo>    Archivo ROSpec personalizado"
    Write-Host "    -Output <archivo>    Archivo de salida personalizado"
    Write-Host "    -SingleAntenna       Usar configuración de una sola antena"
    Write-Host "    -FastReporting       Usar configuración de reporte rápido"
    Write-Host "    -Help                Mostrar esta ayuda"
    Write-Host ""
    Write-Host "EJEMPLOS:" -ForegroundColor Yellow
    Write-Host "    .\test-zebra.ps1"
    Write-Host "    .\test-zebra.ps1 -Host ************* -Port 5084"
    Write-Host "    .\test-zebra.ps1 -SingleAntenna"
    Write-Host "    .\test-zebra.ps1 -FastReporting -Output test-rapido.log"
    Write-Host ""
    Write-Host "MONITOREO EN TIEMPO REAL:" -ForegroundColor Yellow
    Write-Host "    # En otra terminal PowerShell:"
    Write-Host "    Get-Content zebra-tags.log -Wait -Tail 10"
    Write-Host ""
    exit 0
}

# Verificar que Node.js esté disponible
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js detectado: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js no encontrado. Por favor instala Node.js primero." -ForegroundColor Red
    exit 1
}

# Verificar que el archivo del tester exista
if (-not (Test-Path "zebra-rfid-tester.js")) {
    Write-Host "❌ No se encuentra zebra-rfid-tester.js en el directorio actual." -ForegroundColor Red
    exit 1
}

# Construir argumentos para el comando node
$nodeArgs = @("zebra-rfid-tester.js")

if ($Host -ne "") {
    $nodeArgs += "--host", $Host
}

if ($Port -ne "") {
    $nodeArgs += "--port", $Port
}

if ($Config -ne "") {
    $nodeArgs += "--config", $Config
}

if ($Output -ne "") {
    $nodeArgs += "--output", $Output
}

# Configuraciones predefinidas
if ($SingleAntenna) {
    $nodeArgs += "--rospec", "zebra-rospec-single-antenna.json"
    Write-Host "🎯 Usando configuración de una sola antena" -ForegroundColor Cyan
}

if ($FastReporting) {
    $nodeArgs += "--rospec", "zebra-rospec-fast-reporting.json"
    Write-Host "⚡ Usando configuración de reporte rápido" -ForegroundColor Cyan
}

if ($ROSpec -ne "") {
    $nodeArgs += "--rospec", $ROSpec
}

Write-Host "🚀 Iniciando Zebra FX9600 RFID Tester..." -ForegroundColor Green
Write-Host "📋 Comando: node $($nodeArgs -join ' ')" -ForegroundColor Gray
Write-Host ""
Write-Host "💡 TIPS:" -ForegroundColor Yellow
Write-Host "   - Presiona Ctrl+C para detener"
Write-Host "   - En otra terminal ejecuta: Get-Content zebra-tags.log -Wait -Tail 10"
Write-Host "   - Para ver solo EPCs: Get-Content zebra-tags.log -Wait | ForEach-Object { ($_ -split ',')[1] }"
Write-Host ""

# Ejecutar el comando
try {
    & node @nodeArgs
} catch {
    Write-Host "❌ Error ejecutando el tester: $_" -ForegroundColor Red
    exit 1
}
