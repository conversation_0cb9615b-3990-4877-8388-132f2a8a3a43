{"$schema": "https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json", "id": 103, "type": "ADD_ROSPEC", "data": {"ROSpec": {"ROSpecID": 3, "Priority": 0, "CurrentState": "Disabled", "ROBoundarySpec": {"ROSpecStartTrigger": {"ROSpecStartTriggerType": "Immediate"}, "ROSpecStopTrigger": {"ROSpecStopTriggerType": "<PERSON><PERSON>"}}, "AISpec": {"AntennaIDs": [1, 2, 3, 4], "AISpecStopTrigger": {"AISpecStopTriggerType": "Duration", "DurationTriggerValue": 250}, "InventoryParameterSpec": {"InventoryParameterSpecID": 1, "ProtocolID": "EPCGlobalClass1Gen2"}}, "ROReportSpec": {"ROReportTrigger": "Upon_N_Tags_Or_End_Of_AISpec", "N": 5, "TagReportContentSelector": {"EnableROSpecID": false, "EnableSpecIndex": false, "EnableInventoryParameterSpecID": false, "EnableAntennaID": true, "EnableChannelIndex": false, "EnablePeakRSSI": true, "EnableFirstSeenTimestamp": true, "EnableLastSeenTimestamp": true, "EnableTagSeenCount": true, "EnableAccessSpecID": false}}}}}