@echo off
REM Script batch para facilitar el testing del Zebra FX9600
REM Uso: test-zebra.bat [host] [port]

echo 🦓 Zebra FX9600 RFID Reader Tester
echo ===================================

REM Verificar que Node.js esté disponible
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no encontrado. Por favor instala Node.js primero.
    pause
    exit /b 1
)

REM Verificar que el archivo del tester exista
if not exist "zebra-rfid-tester.js" (
    echo ❌ No se encuentra zebra-rfid-tester.js en el directorio actual.
    pause
    exit /b 1
)

echo ✅ Node.js detectado
echo.

REM Construir comando
set CMD=node zebra-rfid-tester.js

REM Si se proporciona host como primer parámetro
if not "%1"=="" (
    set CMD=%CMD% --host %1
    echo 🌐 Usando host: %1
)

REM Si se proporciona puerto como segundo parámetro
if not "%2"=="" (
    set CMD=%CMD% --port %2
    echo 🔌 Usando puerto: %2
)

echo.
echo 💡 TIPS:
echo    - Presiona Ctrl+C para detener
echo    - En otra terminal ejecuta: type zebra-tags.log
echo    - Para monitoreo continuo: powershell "Get-Content zebra-tags.log -Wait -Tail 10"
echo.

REM Ejecutar el comando
echo 🚀 Ejecutando: %CMD%
echo.
%CMD%

pause
