Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB1DD60000 ntdll.dll
7FFB1C710000 KERNEL32.DLL
7FFB1B200000 KERNELBASE.dll
7FFB1CBA0000 USER32.dll
7FFB1BAC0000 win32u.dll
7FFB1D700000 GDI32.dll
7FFB1B780000 gdi32full.dll
7FFB1B980000 msvcp_win.dll
7FFB1AF30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB1BD60000 advapi32.dll
7FFB1D650000 msvcrt.dll
7FFB1D7C0000 sechost.dll
7FFB1D0A0000 RPCRT4.dll
7FFB1A380000 CRYPTBASE.DLL
7FFB1AE90000 bcryptPrimitives.dll
7FFB1BD20000 IMM32.DLL
