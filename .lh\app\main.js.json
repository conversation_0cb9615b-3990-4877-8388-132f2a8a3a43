{"sourceFile": "app/main.js", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1757159162827, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1757159162827, "name": "Commit-0", "content": "const {app, BrowserWindow} = require('electron')\r\nconst path = require('path')\r\nconst url = require('url')\r\n\r\n// Keep a global reference of the window object, if you don't, the window will\r\n// be closed automatically when the JavaScript object is garbage collected.\r\nvar win\r\n\r\nfunction createWindow () {\r\n  // Create the browser window.\r\n  win = new BrowserWindow({\r\n    autoHideMenuBar: true,\r\n    width: 600,\r\n    height: 700,\r\n    backgroundColor: '#444444',\r\n    icon: path.join(__dirname, 'www/images/isotipo.png'),\r\n    webPreferences: {\r\n        nodeIntegration: true,\r\n        contextIsolation: false,\r\n        allowedDomains: ['https://www.example.com', 'https://www.google.com'],\r\n    }\r\n  });\r\n\r\n  // and load the index.html of the app.\r\n  win.loadURL(url.format({\r\n    pathname: path.join(__dirname, 'www/index.html'),\r\n    protocol: 'file:',\r\n    slashes: true\r\n  }))\r\n\r\n  // Open the DevTools.\r\n  win.webContents.openDevTools()\r\n\r\n  // Emitted when the window is closed.\r\n  win.on('closed', () => {\r\n    // Dereference the window object, usually you would store windows\r\n    // in an array if your app supports multi windows, this is the time\r\n    // when you should delete the corresponding element.\r\n    win = null\r\n  })\r\n}\r\n\r\n// This method will be called when Electron has finished\r\n// initialization and is ready to create browser windows.\r\n// Some APIs can only be used after this event occurs.\r\napp.on('ready', createWindow)\r\n\r\n// Quit when all windows are closed.\r\napp.on('window-all-closed', () => {\r\n  // On macOS it is common for applications and their menu bar\r\n  // to stay active until the user quits explicitly with Cmd + Q\r\n  if (process.platform !== 'darwin') {\r\n    app.quit()\r\n  }\r\n})\r\n\r\napp.on('activate', () => {\r\n  // On macOS it's common to re-create a window in the app when the\r\n  // dock icon is clicked and there are no other windows open.\r\n  if (win === null) {\r\n    createWindow()\r\n  }\r\n})\r\n\r\n// In this file you can include the rest of your app's specific main process\r\n// code. You can also put them in separate files and require them here.\r\n"}]}