
var cronopic3 = {

    'config': {
        'enter': true,
        'baudios': 9600,
        'puerto': false
    },

    'serialport': null,

    conectar: function () {
        var that = this;

        var SerialPortClass = require("serialport");
        this.serialport = new SerialPortClass(this.config.puerto, {baudRate: this.config.baudios});

        this.serialport.on('open', function () {
            console.log('CronoPic1 conectado en el puerto ' + that.config.puerto);
        });
        this.serialport.on('data', function(data) {
            that.recibir(data);
        });

    },

    desconectar: function () {
        var that = this;
        this.serialport.close(function (err) {
            console.log('Reader1 desconectado del puerto ' + that.config.puerto, err);
        });
    },

    recibir: function (data) {
        console.log(data)
        if (!this.validar(data))
            return false;

        var lectura = $(".crono-esperando").last().parent();
        if (this.enter == 'esperar' && lectura.length)
            app.esperando(lectura);

        else
            app.crono();
    },

    validar: function (data) {
        if (data[0] == 114) // r minúsculas
            return true;
        else
            return false;
   }

}