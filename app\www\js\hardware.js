
var hardware = {

    conexiones: [],

    initialize: function () {
        this.desconectar();
//        document.addEventListener("deviceready", hardware.onDeviceReady, false);
//        document.addEventListener("DOMContentLoaded", hardware.onDeviceReady, false);
    },

    onDeviceReady: function () {
        try {
            hardwares.forEach(function(element) {
                switch (element.dispositivo) {
                    case 'reader1': var conexion = reader1; break;
                    case 'reader2': var conexion = reader2; break;
                    case 'reader3': var conexion = reader3; break;
                    case 'chafon_810': var conexion = chafon_810; break;
                    case 'reader_zebra_fx9600': var conexion = reader_zebra_fx9600; break;
                    case 'cronopic1': var conexion = cronopic1; break;
                    case 'cronopic2': var conexion = cronopic2; break;
                    case 'cronopic3': var conexion = cronopic3; break;
                    case 'cronopic4': var conexion = cronopic4; break;
                    case 'cronopic5': var conexion = cronopic5; break;
                    case 'cronopic6': var conexion = cronopic6; break;
                    case 'fotocelula1': var conexion = fotocelula1; break;
                }
                conexion.config.puerto = element.puerto;
                hardware.conexiones.push(conexion);
            })
            delay(hardware.conectar(), 2000);
        } catch (e) {
            hardware.desconectar();
            alerta('Hardware no conectado');
            console.log('Hardware no conectado: ', e.message);
        }
    },

    configurar: function () {

        var hardwareForm = $('#hardwareForm');
        for (i = 1; i <= 1; i++) { // Permito una sola conexión por ahora
            var dispositivo = hardwareForm.find("select[name=dispositivo"+i+"] option:selected").val();
            var puerto = hardwareForm.find("select[name=puerto"+i+"] option:selected").val();

            if (PLATFORM == 'windows') {
                window.rfid_crono = hardwareForm.find("select[name=rfid_crono] option:selected").val();
                window.sonido = hardwareForm.find("input[name=sonido]").is(':checked');
                // window.intervalo = hardwareForm.find("select[name=intervalo] option:selected").val();
                window.rebote = hardwareForm.find("select[name=rebote] option:selected").val();
                window.tag_prefijo = hardwareForm.find("input[name=tag_prefijo]").val();
                window.antena_1 = hardwareForm.find("input[name=antena_1]").is(':checked');
                window.antena_2 = hardwareForm.find("input[name=antena_2]").is(':checked');
                window.antena_3 = hardwareForm.find("input[name=antena_3]").is(':checked');
                window.antena_4 = hardwareForm.find("input[name=antena_4]").is(':checked');
                window.antena_5 = hardwareForm.find("input[name=antena_5]").is(':checked');
                window.antena_6 = hardwareForm.find("input[name=antena_6]").is(':checked');
                window.antena_7 = hardwareForm.find("input[name=antena_7]").is(':checked');
                window.antena_8 = hardwareForm.find("input[name=antena_8]").is(':checked');
                // If all antena_* is false, set antenna_1 to true
                if (!window.antena_1 && !window.antena_2 && !window.antena_3 && !window.antena_4 && !window.antena_5 && !window.antena_6 && !window.antena_7 && !window.antena_8) {
                    window.antena_1 = true;
                }
            }
        }
//        if (hardwares.length < 1 || hardwares[0].dispositivo != dispositivo)
//        hardware.desconectar();
//        hardwares = [];

        if (dispositivo) {
            hardwares.push({"dispositivo": dispositivo, "puerto": puerto});
        }
        setConfig('hardwares', hardwares);
        hardware.onDeviceReady();

    },

    desconectar: function () {
        try {
            for (i = 0; i < hardware.conexiones.length; i++) {
                hardware.conexiones[i].desconectar();
            }
        } catch (err) {
            alerta('Desconectando hardware');
            // console.log('Desconectando hardware: ', err);
        }
        hardware.conexiones = [];
        hardwares = [];
        setConfig('hardwares', hardwares);
    },

    conectar: function () {
        for (i = 0; i < hardware.conexiones.length; i++) {
            hardware.conexiones[i].conectar();
        }
    }

}
