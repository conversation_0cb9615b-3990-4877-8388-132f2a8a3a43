.ui-page-theme-a .ui-btn:focus,
html .ui-bar-a .ui-btn:focus,
html .ui-body-a .ui-btn:focus,
html body .ui-group-theme-a .ui-btn:focus,
html head + body .ui-btn.ui-btn-a:focus,
.ui-page-theme-a .ui-focus,
html .ui-bar-a .ui-focus,
html .ui-body-a .ui-focus,
html body .ui-group-theme-a .ui-focus,
html head + body .ui-btn-a.ui-focus,
html head + body .ui-body-a.ui-focus {
    box-shadow: 0px 0px 12px #f7931e;
}

.ui-overlay-a,
.ui-page-theme-a,
.ui-page-theme-a .ui-panel-wrapper {
    background-color: #444;
}

.resultado {
    font-size: 12px;
}

.accion {
    font-size: 9px;
    font-style: italic;
}

.idparticipante {
    margin-left: -16px;
    font-weight: bold;
    color: #f7931e;
}

.contador {
    margin-left: 25px;
}

#totales {
    margin: 5px;
    text-align: center;
}

#consola {
    display: flex;
    flex-direction: column-reverse;
    height: 250px;
    overflow: scroll;
    background-color: black;
    color: white;
    text-align: center;
    margin: 5px;
    padding: 5px;
}

#crono-keys {
    max-width: 400px;
    margin: auto;
}

#crono-btn {
    position: fixed;
    bottom: 5px;
    z-index: 1000;
    width: 110px;
    height: 110px;
    left: 50%;
    margin-left: -55px;
}

#crono {
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
    width: 100%;
    height: 108px;
    margin: 0;
    padding: 0;
    border: none;
    box-shadow: rgba(0, 0, 0, 0.65) 0px 5px 25px;
}

#crono img {
    width: 100%;
}

.ui-footer {
    box-shadow: rgba(0, 0, 0, 0.55) 0px 5px 15px;
}

.ui-footer-fixed {
    z-index: 100;
}

#reenviar-btn {
    float: right;
    margin-right: 30px;
    background: grey;
}

#multicrono {
    background: orange;
    display: block;
}

#buscador-btn,
#qr,
#esperar {
    height: 25px;
 }
