#!/usr/bin/env node

/**
 * Zebra FX9600 RFID Reader Tester
 * Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP
 *
 * Uso:
 *   node zebra-rfid-tester.js [opciones]
 *
 * Opciones:
 *   --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)
 *   --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)
 *   --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)
 *   --host <ip>          IP del reader (override config)
 *   --port <puerto>      Puerto del reader (override config)
 *   --help               Mostrar ayuda
 */

const fs = require('fs');
const path = require('path');

// Configuración por defecto
const DEFAULT_CONFIG = {
    host: '*************',
    port: 5084,
    timeout: 10000,
    reconnectInterval: 5000,
    maxReconnectAttempts: 5
};

const DEFAULT_FILES = {
    config: 'zebra-config.json',
    rospec: 'zebra-rospec.json',
    output: 'zebra-tags.log'
};

class ZebraRFIDTester {
    constructor(options = {}) {
        this.options = { ...DEFAULT_FILES, ...options };
        this.config = null;
        this.rospecConfig = null;
        this.connection = null;
        this.isConnected = false;
        this.isRunning = false;
        this.reconnectAttempts = 0;
        this.tagCount = 0;
        this.startTime = null;
        this.outputStream = null;
        this.gracefulShutdown = false;

        // Estadísticas
        this.stats = {
            totalTags: 0,
            uniqueTags: new Set(),
            antennaStats: {},
            lastTagTime: null,
            startTime: null
        };

        // Configurar manejo de señales mejorado
        this.setupSignalHandlers();
    }

    setupSignalHandlers() {
        // Manejar Ctrl+C de forma más robusta
        process.on('SIGINT', () => {
            if (!this.gracefulShutdown) {
                console.log('\n🛑 Ctrl+C detectado - Iniciando cierre seguro...');
                this.gracefulShutdown = true;
                this.stop().then(() => {
                    console.log('✅ Cierre completado');
                    process.exit(0);
                }).catch(() => {
                    console.log('⚠️ Forzando cierre...');
                    process.exit(1);
                });

                // Timeout de seguridad
                setTimeout(() => {
                    console.log('⚠️ Timeout de cierre - Forzando salida');
                    process.exit(1);
                }, 5000);
            }
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 SIGTERM recibido...');
            this.stop().then(() => process.exit(0));
        });

        // Manejar errores no capturados
        process.on('unhandledRejection', (reason) => {
            console.error('❌ Promesa rechazada:', reason);
            if (!this.gracefulShutdown) {
                this.stop().then(() => process.exit(1));
            }
        });

        process.on('uncaughtException', (error) => {
            console.error('❌ Excepción no capturada:', error);
            if (!this.gracefulShutdown) {
                this.stop().then(() => process.exit(1));
            }
        });
    }

    async init() {
        console.log('🚀 Zebra FX9600 RFID Tester - Iniciando...');

        try {
            // Cargar configuración
            await this.loadConfig();

            // Cargar ROSpec
            await this.loadROSpec();

            // Configurar archivo de salida
            this.setupOutput();

            // Cargar librería LLRP
            this.loadLLRPLibrary();

            console.log('✅ Inicialización completada');
            return true;

        } catch (error) {
            console.error('❌ Error en inicialización:', error.message);
            return false;
        }
    }

    loadLLRPLibrary() {
        try {
            console.log('📦 Cargando librería llrpjs...');
            const llrpjs = require('llrpjs');
            this.LLRPClient = llrpjs.LLRPClient;
            this.LLRPCore = llrpjs.LLRPCore;
            console.log('✅ Librería llrpjs cargada correctamente');
        } catch (error) {
            throw new Error(`No se pudo cargar llrpjs: ${error.message}`);
        }
    }

    async loadConfig() {
        const configFile = this.options.config;

        if (!fs.existsSync(configFile)) {
            console.log(`📝 Creando archivo de configuración: ${configFile}`);
            fs.writeFileSync(configFile, JSON.stringify(DEFAULT_CONFIG, null, 2));
        }

        const configData = fs.readFileSync(configFile, 'utf8');
        this.config = JSON.parse(configData);

        // Override con opciones de línea de comandos
        if (this.options.host) this.config.host = this.options.host;
        if (this.options.port) this.config.port = parseInt(this.options.port);

        console.log('📋 Configuración cargada:');
        console.log(`   Host: ${this.config.host}`);
        console.log(`   Puerto: ${this.config.port}`);
        console.log(`   Timeout: ${this.config.timeout}ms`);
    }

    async loadROSpec() {
        const rospecFile = this.options.rospec;

        if (!fs.existsSync(rospecFile)) {
            console.log(`📝 Creando archivo ROSpec por defecto: ${rospecFile}`);
            const defaultROSpec = this.createDefaultROSpec();
            fs.writeFileSync(rospecFile, JSON.stringify(defaultROSpec, null, 2));
        }

        const rospecData = fs.readFileSync(rospecFile, 'utf8');
        this.rospecConfig = JSON.parse(rospecData);

        console.log('📡 ROSpec cargado:');
        console.log(`   ROSpec ID: ${this.rospecConfig.data.ROSpec.ROSpecID}`);
        console.log(`   Antenas: ${this.rospecConfig.data.ROSpec.AISpec.AntennaIDs.join(', ')}`);
    }

    createDefaultROSpec() {
        // ROSpec basado en la configuración real del Zebra que funciona
        return {
            "$schema": "https://llrpjs.github.io/schema/core/encoding/json/1.0/llrp-1x0.schema.json",
            "id": 103,
            "type": "ADD_ROSPEC",
            "data": {
                "ROSpec": {
                    "ROSpecID": 1,
                    "Priority": 0,
                    "CurrentState": "Disabled",
                    "ROBoundarySpec": {
                        "ROSpecStartTrigger": {
                            "ROSpecStartTriggerType": "Immediate"
                        },
                        "ROSpecStopTrigger": {
                            "ROSpecStopTriggerType": "Immediate"
                        }
                    },
                    "AISpec": {
                        "AntennaIDs": [1, 2, 3, 4, 5, 6, 7, 8], // Todas las 8 antenas como en la config real
                        "AISpecStopTrigger": {
                            "AISpecStopTriggerType": "Duration",
                            "DurationTriggerValue": 400 // 400ms como en la config real
                        },
                        "InventoryParameterSpec": {
                            "InventoryParameterSpecID": 1,
                            "ProtocolID": "EPCGlobalClass1Gen2"
                        }
                    },
                    "ROReportSpec": {
                        "ROReportTrigger": "Upon_N_Tags_Or_End_Of_AISpec",
                        "N": 1, // Tag Report Trigger: 1 como en la config real
                        "TagReportContentSelector": {
                            "EnableROSpecID": false,
                            "EnableSpecIndex": false,
                            "EnableInventoryParameterSpecID": false,
                            "EnableAntennaID": true,
                            "EnableChannelIndex": false,
                            "EnablePeakRSSI": true,
                            "EnableFirstSeenTimestamp": true,
                            "EnableLastSeenTimestamp": true,
                            "EnableTagSeenCount": true,
                            "EnableAccessSpecID": false
                        }
                    }
                }
            }
        };
    }

    setupOutput() {
        const outputFile = this.options.output;
        console.log(`📄 Archivo de salida: ${outputFile}`);

        // Crear stream de escritura
        this.outputStream = fs.createWriteStream(outputFile, { flags: 'a' });

        // Escribir header
        const header = `\n=== SESIÓN INICIADA: ${new Date().toISOString()} ===\n`;
        this.outputStream.write(header);
        console.log(`📝 Logs se guardarán en: ${outputFile}`);
    }

    async connect() {
        try {
            console.log('🔗 Conectando al reader...');

            // Crear cliente LLRP
            this.connection = new this.LLRPClient({
                host: this.config.host,
                port: this.config.port
            });

            // Configurar event handlers
            this.setupEventHandlers();

            // Conectar con timeout
            await Promise.race([
                this.connection.connect(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout de conexión')), this.config.timeout)
                )
            ]);

            this.isConnected = true;
            this.reconnectAttempts = 0;
            console.log('✅ Conectado al reader Zebra FX9600');

            return true;

        } catch (error) {
            console.error('❌ Error de conexión:', error.message);
            this.isConnected = false;
            return false;
        }
    }

    setupEventHandlers() {
        const that = this;

        this.connection.on('error', function(error) {
            console.error('❌ ERROR DE CONEXIÓN:', error.message);
            that.isConnected = false;
            if (that.isRunning && !that.gracefulShutdown) {
                that.scheduleReconnect();
            }
        });

        this.connection.on('connect', function() {
            console.log('🔗 Evento CONNECT recibido');
        });

        this.connection.on('disconnect', function() {
            console.log('🔌 Evento DISCONNECT recibido');
            that.isConnected = false;
            if (that.isRunning && !that.gracefulShutdown) {
                that.scheduleReconnect();
            }
        });

        // Handler para reportes de tags RFID
        this.connection.on('RO_ACCESS_REPORT', function(msg) {
            that.processTagReport(msg);
        });

        // Handler para notificaciones de eventos del reader
        this.connection.on('READER_EVENT_NOTIFICATION', function(msg) {
            console.log('📢 READER_EVENT_NOTIFICATION recibido');
        });
    }

    processTagReport(msg) {
        try {
            const tagReportData = msg.getTagReportData ? msg.getTagReportData() : [];
            const tags = Array.isArray(tagReportData) ? tagReportData : [tagReportData];

            for (const tag of tags) {
                if (tag && tag.getEPCParameter) {
                    const epcParam = tag.getEPCParameter();
                    const epc = epcParam ? epcParam.getEPC() : null;
                    const antennaId = tag.getAntennaID ? tag.getAntennaID() : 'Unknown';
                    const rssi = tag.getPeakRSSI ? tag.getPeakRSSI() : null;
                    const timestamp = new Date().toISOString();

                    if (epc) {
                        this.logTag(epc, antennaId, rssi, timestamp);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Error procesando reporte de tags:', error.message);
        }
    }

    logTag(epc, antenna, rssi, timestamp) {
        this.stats.totalTags++;
        this.stats.uniqueTags.add(epc);
        this.stats.lastTagTime = timestamp;

        if (!this.stats.antennaStats[antenna]) {
            this.stats.antennaStats[antenna] = 0;
        }
        this.stats.antennaStats[antenna]++;

        // Log a consola
        const logMessage = `📡 TAG: ${epc} | Antena: ${antenna} | RSSI: ${rssi} | ${timestamp}`;
        console.log(logMessage);

        // Log a archivo
        const fileMessage = `${timestamp},${epc},${antenna},${rssi}\n`;
        this.outputStream.write(fileMessage);

        // Mostrar estadísticas cada 10 tags
        if (this.stats.totalTags % 10 === 0) {
            this.showStats();
        }
    }

    showStats() {
        const runtime = this.stats.startTime ?
            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;

        console.log('\n📊 ESTADÍSTICAS:');
        console.log(`   Total tags: ${this.stats.totalTags}`);
        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);
        console.log(`   Tiempo ejecución: ${runtime}s`);
        console.log(`   Tags por antena:`, this.stats.antennaStats);
        console.log('');
    }

    async initializeReader() {
        try {
            console.log('🔧 Inicializando reader...');

            // Esperar confirmación de conexión
            await this.waitForConnectionConfirmation();

            // Limpiar ROSpecs existentes
            await this.cleanupROSpecs();

            // Crear y configurar ROSpec
            await this.setupROSpec();

            console.log('✅ Reader inicializado correctamente');
            return true;

        } catch (error) {
            console.error('❌ Error inicializando reader:', error.message);
            return false;
        }
    }

    async waitForConnectionConfirmation() {
        console.log('⏳ Esperando confirmación de conexión...');

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Timeout esperando confirmación'));
            }, this.config.timeout);

            const checkConnection = () => {
                if (this.isConnected) {
                    clearTimeout(timeout);
                    resolve();
                } else {
                    setTimeout(checkConnection, 100);
                }
            };

            checkConnection();
        });
    }

    async cleanupROSpecs() {
        try {
            console.log('🗑️ Limpiando ROSpecs existentes...');

            // Obtener ROSpecs existentes
            const response = await Promise.race([
                this.connection.transact(new this.LLRPCore.GET_ROSPECS()),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Timeout en GET_ROSPECS')), 5000)
                )
            ]);

            // Procesar ROSpecs
            let rospecs = response.getROSpec ? response.getROSpec() : [];
            if (!Array.isArray(rospecs)) {
                rospecs = rospecs ? [rospecs] : [];
            }

            // Eliminar cada ROSpec
            for (const rospec of rospecs) {
                const rospecId = rospec.getROSpecID ? rospec.getROSpecID() : null;
                if (rospecId) {
                    try {
                        await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({
                            data: { ROSpecID: rospecId }
                        }));
                        console.log(`   ✅ ROSpec ${rospecId} eliminado`);
                    } catch (error) {
                        console.log(`   ⚠️ Error eliminando ROSpec ${rospecId}:`, error.message);
                    }
                }
            }

        } catch (error) {
            console.log('⚠️ Error en limpieza de ROSpecs:', error.message);
        }
    }

    async setupROSpec() {
        try {
            console.log('📡 Configurando ROSpec...');

            // Crear ROSpec
            await this.connection.transact(new this.LLRPCore.ADD_ROSPEC(this.rospecConfig));
            console.log('✅ ROSpec creado');

            // Habilitar ROSpec
            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;
            await this.connection.transact(new this.LLRPCore.ENABLE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
            console.log('✅ ROSpec habilitado');

            // Iniciar ROSpec
            await this.connection.transact(new this.LLRPCore.START_ROSPEC({
                data: { ROSpecID: rospecId }
            }));
            console.log('✅ ROSpec iniciado');

            console.log('🎉 Reader listo para detectar tags');

        } catch (error) {
            throw new Error(`Error configurando ROSpec: ${error.message}`);
        }
    }

    async stopROSpec() {
        try {
            const rospecId = this.rospecConfig.data.ROSpec.ROSpecID;

            console.log('🛑 Deteniendo ROSpec...');
            await this.connection.transact(new this.LLRPCore.STOP_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            console.log('🗑️ Eliminando ROSpec...');
            await this.connection.transact(new this.LLRPCore.DELETE_ROSPEC({
                data: { ROSpecID: rospecId }
            }));

            console.log('✅ ROSpec detenido y eliminado');

        } catch (error) {
            console.log('⚠️ Error deteniendo ROSpec:', error.message);
        }
    }

    scheduleReconnect() {
        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            console.error('❌ Máximo número de intentos de reconexión alcanzado');
            this.stop();
            return;
        }

        this.reconnectAttempts++;
        console.log(`🔄 Reintentando conexión en ${this.config.reconnectInterval}ms (intento ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

        setTimeout(async () => {
            if (this.isRunning) {
                const connected = await this.connect();
                if (connected) {
                    await this.initializeReader();
                }
            }
        }, this.config.reconnectInterval);
    }

    async start() {
        console.log('🚀 Iniciando lectura de tags...');
        this.isRunning = true;
        this.stats.startTime = Date.now();

        // Conectar al reader
        const connected = await this.connect();
        if (!connected) {
            console.error('❌ No se pudo conectar al reader');
            return false;
        }

        // Inicializar reader
        const initialized = await this.initializeReader();
        if (!initialized) {
            console.error('❌ No se pudo inicializar el reader');
            return false;
        }

        console.log('✅ Lectura de tags iniciada');
        console.log('📝 Presiona Ctrl+C para detener');

        return true;
    }

    async stop() {
        console.log('🛑 Deteniendo lectura de tags...');
        this.isRunning = false;

        if (this.connection && this.isConnected) {
            await this.stopROSpec();

            try {
                await this.connection.transact(new this.LLRPCore.CLOSE_CONNECTION());
                this.connection.disconnect();
            } catch (error) {
                console.log('⚠️ Error cerrando conexión:', error.message);
            }
        }

        if (this.outputStream) {
            const footer = `=== SESIÓN FINALIZADA: ${new Date().toISOString()} ===\n`;
            this.outputStream.write(footer);
            this.outputStream.end();
        }

        this.showFinalStats();
        console.log('✅ Lectura detenida');
    }

    showFinalStats() {
        const runtime = this.stats.startTime ?
            Math.round((Date.now() - this.stats.startTime) / 1000) : 0;

        console.log('\n📊 ESTADÍSTICAS FINALES:');
        console.log(`   Total tags detectados: ${this.stats.totalTags}`);
        console.log(`   Tags únicos: ${this.stats.uniqueTags.size}`);
        console.log(`   Tiempo total: ${runtime}s`);
        console.log(`   Promedio: ${runtime > 0 ? (this.stats.totalTags / runtime).toFixed(2) : 0} tags/s`);
        console.log(`   Distribución por antena:`, this.stats.antennaStats);
        console.log(`   Último tag: ${this.stats.lastTagTime || 'N/A'}`);
        console.log('');
    }
}

// Función para parsear argumentos de línea de comandos
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];

        switch (arg) {
            case '--help':
            case '-h':
                showHelp();
                process.exit(0);
                break;
            case '--config':
                options.config = args[++i];
                break;
            case '--rospec':
                options.rospec = args[++i];
                break;
            case '--output':
                options.output = args[++i];
                break;
            case '--host':
                options.host = args[++i];
                break;
            case '--port':
                options.port = args[++i];
                break;
            default:
                if (arg.startsWith('--')) {
                    console.error(`❌ Opción desconocida: ${arg}`);
                    showHelp();
                    process.exit(1);
                }
        }
    }

    return options;
}

function showHelp() {
    console.log(`
🦓 Zebra FX9600 RFID Reader Tester

Herramienta de testing para el reader RFID Zebra FX9600 usando protocolo LLRP

USO:
    node zebra-rfid-tester.js [opciones]

OPCIONES:
    --config <archivo>    Archivo de configuración JSON (default: zebra-config.json)
    --rospec <archivo>    Archivo ROSpec JSON (default: zebra-rospec.json)
    --output <archivo>    Archivo de salida para logs (default: zebra-tags.log)
    --host <ip>          IP del reader (override config)
    --port <puerto>      Puerto del reader (override config)
    --help, -h           Mostrar esta ayuda

EJEMPLOS:
    # Usar configuración por defecto
    node zebra-rfid-tester.js

    # Especificar IP y puerto
    node zebra-rfid-tester.js --host ************* --port 5084

    # Usar archivos de configuración personalizados
    node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json

    # Guardar logs en archivo específico
    node zebra-rfid-tester.js --output mis-tags.log

ARCHIVOS GENERADOS:
    zebra-config.json     Configuración del reader (IP, puerto, timeouts)
    zebra-rospec.json     Configuración ROSpec (antenas, parámetros RFID)
    zebra-tags.log        Log de tags detectados (CSV format)

FORMATO DEL LOG:
    timestamp,epc,antenna,rssi
    2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45

MONITOREO EN TIEMPO REAL:
    # En otra terminal, para ver tags en tiempo real:
    tail -f zebra-tags.log

    # Para ver solo los EPCs:
    tail -f zebra-tags.log | cut -d',' -f2

    # Para contar tags únicos:
    tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l
`);
}

// Función principal
async function main() {
    console.log('🦓 Zebra FX9600 RFID Reader Tester v1.0');
    console.log('=====================================\n');

    // Parsear argumentos
    const options = parseArgs();

    // Crear instancia del tester
    const tester = new ZebraRFIDTester(options);

    // Los manejadores de señales ya están configurados en el constructor de la clase

    try {
        // Inicializar
        const initialized = await tester.init();
        if (!initialized) {
            console.error('❌ Error en inicialización');
            process.exit(1);
        }

        // Iniciar lectura
        const started = await tester.start();
        if (!started) {
            console.error('❌ Error iniciando lectura');
            process.exit(1);
        }

        // Mantener el proceso vivo
        console.log('🔄 Leyendo tags... (Ctrl+C para detener)');

        // Mostrar estadísticas cada 30 segundos
        setInterval(() => {
            if (tester.isRunning) {
                tester.showStats();
            }
        }, 30000);

    } catch (error) {
        console.error('❌ Error fatal:', error.message);
        await tester.stop();
        process.exit(1);
    }
}

// Ejecutar solo si es el archivo principal
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Error en main:', error);
        process.exit(1);
    });
}

module.exports = ZebraRFIDTester;
