# Zebra FX9600 RFID Reader Tester v2.0

Herramienta de testing mejorada para el reader RFID Zebra FX9600 usando protocolo LLRP.

## 🆕 Mejoras en v2.0

- ✅ **Ctrl+C funciona correctamente** - Cierre seguro y limpio
- ✅ **Configuración basada en el Zebra real** - Usa parámetros que funcionan
- ✅ **8 antenas habilitadas** - Como en la configuración real del FX9600
- ✅ **Timeouts optimizados** - 15 segundos para conexiones más estables
- ✅ **Manejo robusto de errores** - Reconexión automática mejorada

## Instalación

1. Asegúrate de tener Node.js instalado
2. Instala las dependencias:
   ```bash
   cd app
   npm install llrpjs
   ```

## Uso Básico

### Comando simple
```bash
node zebra-rfid-tester.js
```

### Especificar IP y puerto del reader
```bash
node zebra-rfid-tester.js --host ************* --port 5084
```

### Usar archivos de configuración personalizados
```bash
node zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json
```

### Guardar logs en archivo específico
```bash
node zebra-rfid-tester.js --output mis-tags.log
```

## Archivos de Configuración

### zebra-config.json
Configuración del reader (IP, puerto, timeouts):
```json
{
  "host": "*************",
  "port": 5084,
  "timeout": 10000,
  "reconnectInterval": 5000,
  "maxReconnectAttempts": 5
}
```

### zebra-rospec.json
Configuración ROSpec (antenas, parámetros RFID). Puedes modificar:
- `AntennaIDs`: Array de antenas a usar (ej: [1, 2, 3, 4])
- `ROSpecID`: ID único del ROSpec
- `N`: Número de tags por reporte
- Otros parámetros LLRP según necesidades

## Monitoreo en Tiempo Real

### Ver tags en tiempo real (en otra terminal)
```bash
tail -f zebra-tags.log
```

### Ver solo los EPCs
```bash
tail -f zebra-tags.log | cut -d',' -f2
```

### Contar tags únicos
```bash
tail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l
```

### Filtrar por antena específica
```bash
tail -f zebra-tags.log | grep ",3," # Solo antena 3
```

## Formato del Log

El archivo de salida (zebra-tags.log) tiene formato CSV:
```
timestamp,epc,antenna,rssi
2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45
2024-01-01T12:00:01.000Z,E20000987654321098765432,2,-52
```

## Opciones de Línea de Comandos

- `--config <archivo>`: Archivo de configuración JSON
- `--rospec <archivo>`: Archivo ROSpec JSON  
- `--output <archivo>`: Archivo de salida para logs
- `--host <ip>`: IP del reader (override config)
- `--port <puerto>`: Puerto del reader (override config)
- `--help, -h`: Mostrar ayuda

## Ejemplos de Configuración ROSpec

### Solo antena 1
```json
"AntennaIDs": [1]
```

### Antenas 1 y 3
```json
"AntennaIDs": [1, 3]
```

### Todas las antenas
```json
"AntennaIDs": [1, 2, 3, 4]
```

### Reportar cada 5 tags
```json
"ROReportSpec": {
  "ROReportTrigger": "Upon_N_Tags_Or_End_Of_AISpec",
  "N": 5,
  ...
}
```

## Solución de Problemas

### Error de conexión
1. Verifica que el reader esté encendido y conectado a la red
2. Verifica la IP en zebra-config.json
3. Asegúrate de que el puerto 5084 esté abierto

### No se detectan tags
1. Verifica que las antenas estén conectadas
2. Revisa la configuración de antenas en zebra-rospec.json
3. Asegúrate de que haya tags RFID en el rango de las antenas

### Desconexiones frecuentes
1. Aumenta el timeout en zebra-config.json
2. Verifica la estabilidad de la red
3. Revisa los logs del reader

## Detener la Herramienta

Presiona `Ctrl+C` para detener la lectura de tags de forma segura.
