{"sourceFile": "app/README-zebra-tester.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1758058513844, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1758058513843, "name": "Commit-0", "content": "# Zebra FX9600 RFID Reader Tester v2.0\n\nHerramienta de testing mejorada para el reader RFID Zebra FX9600 usando protocolo LLRP.\n\n## 🆕 Mejoras en v2.0\n\n- ✅ **Ctrl+C funciona correctamente** - Cierre seguro y limpio\n- ✅ **Configuración basada en el Zebra real** - Usa parámetros que funcionan\n- ✅ **8 antenas habilitadas** - Como en la configuración real del FX9600\n- ✅ **Timeouts optimizados** - 15 segundos para conexiones más estables\n- ✅ **Manejo robusto de errores** - Reconexión automática mejorada\n\n## Instalación\n\n1. Asegúrate de tener Node.js instalado\n2. Instala las dependencias:\n   ```bash\n   cd app\n   npm install llrpjs\n   ```\n\n## Uso Básico\n\n### Comando simple\n```bash\nnode zebra-rfid-tester.js\n```\n\n### Especificar IP y puerto del reader\n```bash\nnode zebra-rfid-tester.js --host ************* --port 5084\n```\n\n### Usar archivos de configuración personalizados\n```bash\nnode zebra-rfid-tester.js --config mi-config.json --rospec mi-rospec.json\n```\n\n### Guardar logs en archivo específico\n```bash\nnode zebra-rfid-tester.js --output mis-tags.log\n```\n\n## Archivos de Configuración\n\n### zebra-config.json\nConfiguración del reader (IP, puerto, timeouts):\n```json\n{\n  \"host\": \"*************\",\n  \"port\": 5084,\n  \"timeout\": 10000,\n  \"reconnectInterval\": 5000,\n  \"maxReconnectAttempts\": 5\n}\n```\n\n### zebra-rospec.json\nConfiguración ROSpec (antenas, parámetros RFID). Puedes modificar:\n- `AntennaIDs`: Array de antenas a usar (ej: [1, 2, 3, 4])\n- `ROSpecID`: ID único del ROSpec\n- `N`: Número de tags por reporte\n- Otros parámetros LLRP según necesidades\n\n## Monitoreo en Tiempo Real\n\n### Ver tags en tiempo real (en otra terminal)\n```bash\ntail -f zebra-tags.log\n```\n\n### Ver solo los EPCs\n```bash\ntail -f zebra-tags.log | cut -d',' -f2\n```\n\n### Contar tags únicos\n```bash\ntail -f zebra-tags.log | cut -d',' -f2 | sort | uniq | wc -l\n```\n\n### Filtrar por antena específica\n```bash\ntail -f zebra-tags.log | grep \",3,\" # Solo antena 3\n```\n\n## Formato del Log\n\nEl archivo de salida (zebra-tags.log) tiene formato CSV:\n```\ntimestamp,epc,antenna,rssi\n2024-01-01T12:00:00.000Z,E20000123456789012345678,1,-45\n2024-01-01T12:00:01.000Z,E20000987654321098765432,2,-52\n```\n\n## Opciones de Línea de Comandos\n\n- `--config <archivo>`: Archivo de configuración JSON\n- `--rospec <archivo>`: Archivo ROSpec JSON  \n- `--output <archivo>`: Archivo de salida para logs\n- `--host <ip>`: IP del reader (override config)\n- `--port <puerto>`: Puerto del reader (override config)\n- `--help, -h`: Mostrar ayuda\n\n## Ejemplos de Configuración ROSpec\n\n### Solo antena 1\n```json\n\"AntennaIDs\": [1]\n```\n\n### Antenas 1 y 3\n```json\n\"AntennaIDs\": [1, 3]\n```\n\n### Todas las antenas\n```json\n\"AntennaIDs\": [1, 2, 3, 4]\n```\n\n### Reportar cada 5 tags\n```json\n\"ROReportSpec\": {\n  \"ROReportTrigger\": \"Upon_N_Tags_Or_End_Of_AISpec\",\n  \"N\": 5,\n  ...\n}\n```\n\n## Solución de Problemas\n\n### Error de conexión\n1. Verifica que el reader esté encendido y conectado a la red\n2. Verifica la IP en zebra-config.json\n3. Asegúrate de que el puerto 5084 esté abierto\n\n### No se detectan tags\n1. Verifica que las antenas estén conectadas\n2. Revisa la configuración de antenas en zebra-rospec.json\n3. Asegúrate de que haya tags RFID en el rango de las antenas\n\n### Desconexiones frecuentes\n1. Aumenta el timeout en zebra-config.json\n2. Verifica la estabilidad de la red\n3. Revisa los logs del reader\n\n## Detener la Herramienta\n\nPresiona `Ctrl+C` para detener la lectura de tags de forma segura.\n"}]}